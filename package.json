{"name": "like-pc", "version": "1.0.0", "description": "智慧点餐系统", "main": "index.js", "repository": "http://git.yyide.com/cloud/like-pc.git", "author": "szj <<EMAIL>>", "license": "MIT", "scripts": {"dev": "vite", "build:dev": "vite build --mode dev", "build:uat": "vite build --mode uat", "build:uatrelease": "vite build --mode uatrelease", "build:production": "vite build --mode production", "preview": "vite preview", "prepare": "husky install"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@tinymce/tinymce-vue": "^6.0.1", "@vueuse/core": "^10.4.1", "ant-design-vue": "4.1.2", "axios": "^1.4.0", "dayjs": "^1.11.9", "echarts": "^5.4.3", "encryptlong": "^3.1.4", "html2canvas": "^1.4.1", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^3.2.0", "qrcode": "^1.5.4", "qs": "^6.11.2", "terser": "^5.21.0", "tinymce": "^7.4.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "^3.3.4", "vue-draggable-next": "^2.2.1", "vue-router": "4", "vuedraggable": "^4.1.0", "xgplayer": "^3.0.20", "xgplayer-music": "^3.0.20", "ydutils": "git+https://root:<EMAIL>/web/ydutils.git#main"}, "devDependencies": {"@unocss/preset-rem-to-px": "^0.55.0", "@unocss/reset": "^0.55.0", "@vitejs/plugin-vue": "^4.2.3", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "^14.0.1", "prettier": "3.0.3", "unocss": "^0.55.0", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.5", "vite-plugin-html": "^3.2.0"}}