.ant-dropdown {
    .ant-dropdown-menu {
        .ant-dropdown-menu-item {
            &:hover {
                color: var(--primary-color);
            }
        }
    }
}

// 弹窗样式修改
.ant-modal {
    .ant-modal-content {
        border-radius: 4px;
        padding: 0;
    }

    .ant-modal-header {
        padding: 16px 24px;
        border-bottom: 1px solid #d9d9d9;
        margin-bottom: 0;
    }

    .ant-modal-footer {
        padding: 16px 24px;
        border-top: 1px solid #d9d9d9;
        margin-top: 0;
    }
}

// 删除弹窗
.ant-modal.y-delModal {
    .ant-modal-content {
        padding: 24px;
    }
}

// dangerous弹窗确定按钮
.ant-modal-confirm-btns {
    .ant-btn-dangerous {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: #fff;
    }
    .ant-btn-default.ant-btn-dangerous:not(:disabled):hover {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: #fff;
    }
}

// 侧边抽屉样式修改
.ant-drawer {
    .ant-drawer-header-title {
        flex-direction: row-reverse;

        .ant-drawer-close {
            margin-right: 0;
        }
    }

    .ant-drawer-content {
        .ant-drawer-footer {
            text-align: center;
            padding: 16px;
        }
    }
}

.ant-btn {
    border-radius: 4px;
}

.ant-input-number {
    width: 100%;
}

.ant-input-affix-wrapper,
.ant-select-selector,
.ant-input-number,
.ant-input-number-input,
.ant-input {
    border-radius: 4px !important;
}

// 表格相关
.ant-table-wrapper .ant-table-thead > tr > th,
.ant-table-wrapper .ant-table-tbody > tr > td,
.ant-table-wrapper tfoot > tr > td {
    padding: 13px 16px;
}

.ant-table-wrapper .ant-table-thead > tr > th::before {
    display: none;
}

.ant-radio-wrapper {
    min-width: 76px;
}

.btn-link-color {
    color: var(--primary-color);
    padding: 0;
    cursor: pointer;
    height: auto;
}

.ellipsis-btn {
    width: 100%;
    overflow: hidden;
    span {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: start;
    }
}

.btn-link-warning {
    color: @warning-color;
    padding: 0;
    cursor: pointer;
    height: auto;
}

.ant-btn-link:not(:disabled):hover {
    color: var(--primary-color);
}

.ant-modal-confirm-body {
    padding-top: 32px;
    padding-left: 10px;
}

.ant-modal-confirm-btns {
    padding-bottom: 24px;
    padding-right: 32px;
}

.ant-form-item {
    // 影响到了全局了
    margin-bottom: 0;
}

.ant-picker {
    width: 100%;
}

.input_group_wrapper {
    .input_left {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        border-right: 0;
        width: calc(50% - 15px);
    }

    .input_right {
        border-left: 0;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        width: calc(50% - 15px);
    }

    .c_icon {
        width: 30px;
        height: 32px;
        text-align: center;
        font-size: 18px;
        color: #bfbfbf;
        pointer-events: none;
        border-top: 1px solid #d9d9d9;
        border-bottom: 1px solid #d9d9d9;
    }
}

.btn-link-delete,
.button-link {
    color: var(--primary-color);
    height: auto;
    line-height: inherit;
    padding: 1px 6px 0 6px;

    &:active span,
    &:hover span {
        color: var(--primary-color);
    }
}

.btn-link-delete {
    color: @error-color;

    &:active span,
    &:hover span {
        color: @error-color;
    }

    &[disabled]:hover span {
        color: @disabled-color;
    }
}

.btn-delete {
    color: @error-color;
    border-color: @error-color;

    &:hover {
        border-color: @error-color !important;
    }

    &:active span,
    &:hover span {
        color: @error-color;
    }

    &[disabled]:hover span {
        color: @disabled-color;
    }
}

.ant-table-content .ant-empty-image {
    position: relative;

    svg {
        opacity: 0;
    }
}

.ant-table-content .ant-empty-image::after {
    content: '';
    width: 180px;
    height: 180px;
    background: url(/empty.png);
    position: absolute;
    z-index: 9;
    top: -50px;
    left: 0;
    right: 0;
    opacity: 1;
    background-size: 100% 100%;
    margin: 0 auto;
}

.ant-table-content .ant-empty-description {
    position: relative;
    top: 50px;
}

.ant-table-content .ant-empty-normal {
    min-height: 140px;
}

.conent_flex {
    overflow: hidden;
}

// 一行自适应隐藏
.y_tree {
    .ant-select-tree-title,
    .ant-tree-title {
        display: flex;
        width: 0;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
