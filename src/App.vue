<template>
    <div>
        <a-config-provider
            :locale="zh_CN"
            :theme="{
                token: {
                    colorPrimary: store.sysColor,
                },
            }"
        >
            <router-view></router-view>
        </a-config-provider>
    </div>
</template>
<script setup>
import zh_CN from 'ant-design-vue/es/locale/zh_CN'
import 'dayjs/locale/zh-cn'
const store = useStore()
document.documentElement.style.setProperty('--primary-color', store.sysColor)
</script>
<style></style>
