export default (url, other = {}) => {
    const userState = reactive({
        loading: false,
        dataSource: [],
        pagination: {
            pageNo: 1,
            pageSize: 10,
            total: 0,
        },
    })
    const getInitData = async () => {
        try {
            userState.loading = true
            const params = {
                ...userState.pagination,
                ...other,
            }
            await http.post(url, params).then(({ data }) => {
                const { pageNo, pageSize, total, list } = data
                userState.dataSource = list
                userState.pagination.pageNo = pageNo
                userState.pagination.pageSize = pageSize
                userState.pagination.total = total
            })
        } catch (error) {
            console.log('getList error: ', error)
        } finally {
            userState.loading = false
        }
    }
    const updateInitData = async () => {
        userState.pagination.pageNo = 1
        userState.pagination.pageSize = 10
        getInitData()
    }
    getInitData()
    return { getInitData, updateInitData, userState }
}
