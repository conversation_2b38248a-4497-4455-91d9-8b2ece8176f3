// 服务端链接
export const SERVER_CONFIG_API = {
    // 配置
    CONFIG: 'config',
    // 搜索列表(搜索)
    SEARCH_COLUMN: 'searchColumn',
    // 是否启用冻结列(表格)
    FREEZE_COLUMN: 'freezeColum',
    // 冻结列列数(表格)
    FREEZE_NUMBER: 'freezeNumber',
    // 冻结操作列(表格)
    FREEZE_OPERATE: 'freezeOperate',
    // table顺序以及是否展示(表格)
    TABLE_COLUMN: 'tableColumn',
}

const useServerConfig = (formCode, formList = []) => {
    const state = reactive({
        // 保存formList下拉选项的list数据
        cacheList: {},
    })

    const condition = reactive({
        list: [],
        configs: {},
    })

    const table = reactive({
        list: [],
        configs: {
            columns: {},
            freeze: {},
        },
    })

    // *********************
    // Life Event Function
    // *********************

    // *********************
    // Service Function
    // *********************

    /** 获取到服务端配置数据 */
    const getSeverConfig = async () => {
        const { data } = await http.post('/marketing/marketingUserHabit/searchCommonInterfaceConfig', {
            formCode,
        })
        updateCondition(data)
        updateTable(data)
    }

    const findItemByKey = (key, item) => {
        const result =
            formList.find(
                i =>
                    // value为时间范围，就拼接起来
                    (Array.isArray(i.value) && key === i.value.join('/')) || key === i.value,
            ) || {}
        return {
            ...result,
            // 选项存在缓存数据使用缓存中的数据
            list: state.cacheList[key] ? state.cacheList[key] : result.list,
            // 使用服务端的名称
            label: item.name,
        }
    }

    /** 处理条件 */
    const updateCondition = data => {
        let list = []
        for (const key in data.condition) {
            const item = findItemByKey(key, data.condition[key])
            list.push(item)
        }
        list = list.filter(Boolean)
        condition.list = list
        condition.configs = data.condition
    }

    /** 更新list下的选项列表 */
    const formEchoData = (key, list) => {
        condition.list = condition.list.map(item => {
            return {
                ...item,
                list: item.value == key ? list : item.list,
            }
        })
        // $ 缓存修改过的formList的列表数据
        state.cacheList = {
            ...state.cacheList,
            [key]: list,
        }
    }

    /** 处理表格数据 */
    const updateTable = data => {
        const list = []
        const { columns, freeze } = data
        for (const key in columns) {
            list.push({
                title: columns[key].name,
                dataIndex: key,
            })
        }
        table.list = list
        table.configs = { columns, freeze }
    }

    /** 更新condition设置 */
    const updateConditionConfig = async updateConfig => {
        let conditionList = []
        for (const key in updateConfig) {
            conditionList.push({
                columnCode: key,
                search: updateConfig[key].checked,
            })
        }
        conditionList.sort((pre, next) => updateConfig[pre.columnCode].order - updateConfig[next.columnCode].order)
        const params = { menuConditionList: conditionList, formCode }
        const result = await http.post('/marketing/marketingUserHabit/adjustSearchColumnCommon', params)
        YMessage.success(result.message)
    }

    /** 更新table设置 */
    const updateTableConfig = async (action, payload) => {
        switch (action) {
            case SERVER_CONFIG_API.FREEZE_COLUMN:
                updateFreezeColum(payload)
                break
            case SERVER_CONFIG_API.FREEZE_NUMBER:
                updateFreezeNumber(payload)
                break
            case SERVER_CONFIG_API.FREEZE_OPERATE:
                updateFreezeOperate(payload)
                break
            case SERVER_CONFIG_API.TABLE_COLUMN:
                updateConfig(payload)
                break
        }
    }

    /** 是否启用冻结列 */
    const updateFreezeColum = async value => {
        const params = { value, formCode }
        const result = await http.post('/marketing/marketingUserHabit/enableFrozenForefrontColumnCommon', params)
        YMessage.success(result.message)
    }

    /** 冻结列列数 */
    const updateFreezeNumber = async value => {
        const params = { value, formCode }
        const result = await http.post('/marketing/marketingUserHabit/frozenForefrontColumnCountCommon', params)
        YMessage.success(result.message)
    }

    /** 冻结操作列 */
    const updateFreezeOperate = async value => {
        const params = { value, formCode }
        const result = await http.post('/marketing/marketingUserHabit/enableFrozenOperateColumnCommon', params)
        YMessage.success(result.message)
    }

    /** 是否启用冻结列 */
    const updateConfig = async updateConfig => {
        let list = []
        for (const key in updateConfig) {
            list.push({
                columnCode: key,
                show: updateConfig[key].checked,
            })
        }
        list.sort((pre, next) => updateConfig[pre.columnCode].order - updateConfig[next.columnCode].order)
        const params = { menuFieldList: list, formCode }
        const result = await http.post('/marketing/marketingUserHabit/adjustShowColumnCommon', params)
        YMessage.success(result.message)
    }

    return { condition, table, formEchoData, getSeverConfig, updateConditionConfig, updateTableConfig }
}

export default useServerConfig
