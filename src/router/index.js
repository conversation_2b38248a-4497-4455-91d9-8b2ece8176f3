import { createRouter, createWebHashHistory } from 'vue-router'
import getRoute from './getRoute'
import config from './config'
const store = useStore()
const sysIdent = sessionStorage.getItem('sysIdent')
const getAsRoute = async () => {
    const [routeArr] = await getRoute()
    // 没有路由就跳转登录
    let redirectUrl = ``

    if (routeArr.length) {
        redirectUrl = routeArr[0]?.children[0]?.path || '/'
        const router = [
            {
                path: '/',
                redirect: redirectUrl,
            },
            {
                component: () => import('@/components/layout/index.vue'),
                redirect: redirectUrl,
                children: [
                    ...routeArr,
                    {
                        path: '/404',
                        name: '404',
                        component: () => import('@/components/common/404.vue'),
                    },
                    {
                        path: '/no-auth',
                        name: 'no-auth',
                        component: () => import('@/components/common/NoAuth.vue'),
                    },
                    {
                        path: '/redirect',
                        name: 'redirect',
                        component: () => import('@/components/common/redirect.vue'),
                    },
                ],
            },
        ]
        return router
    } else {
        YMessage.warning('您还没有权限，请联系管理员')
        setTimeout(() => {
            // 清空缓存
            sessionStorage.clear()
            window?.close()
        }, 2000)
    }
}
const init = async () => {
    let routes = [
        // {
        //     path: '/login',
        //     redirect: '/login/management',
        // },
        // {
        //     path: '/login/:path',
        //     name: 'login',
        //     component: () => import('@/components/login/index.vue'),
        // },
        {
            path: '/:pathMatch(.*)',
            redirect: '/404',
        },
    ]
    const arr = await getAsRoute()
    routes = [...routes, ...arr]
    // if (window.location.hash.indexOf('login') == -1 && window.location.hash.indexOf('messageBoard') == -1) {
    //     const arr = await getAsRoute()
    //     routes = [...routes, ...arr]
    // }
    return Promise.resolve(routes)
}
const asRouter = async () => {
    return new Promise((resolve, reject) => {
        init().then(res => {
            const router = createRouter({
                history: createWebHashHistory(),
                scrollBehavior() {
                    return { top: 0, left: 0 }
                },
                routes: res,
            })
            config(router)
            return resolve(router)
        })
    })
}

export default asRouter
