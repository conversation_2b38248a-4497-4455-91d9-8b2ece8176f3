<template>
    <YModal
        v-model:open="props.open"
        title="同步记录"
        :bodyStyle="{ padding: '24px' }"
        :width="'60%'"
        @cancel="handleCancel"
        :footer="null"
    >
        <ETable
            class="reset-table"
            :columns="syncRecordColumns"
            :data-source="state.dataSource"
            :paginations="state.pagination"
            :loading="state.loading"
            @change="handlesyncRecorTableChange"
        >
            <template #bodyCell="{ column, text, record, index }">
                <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                <template v-else-if="column.dataIndex == 'status'">
                    <a-badge v-if="text" :status="isServing(text)" :text="text" />
                    <span v-else>--</span>
                    <a-tooltip placement="top" color="#fff">
                        <template #title>
                            <span style="color: #000">{{ record.renson }}</span>
                        </template>
                        <img v-if="record.status == '异常'" class="outlinebeifen" src="@/assets/images/outlinebeifen.png" />
                    </a-tooltip>
                </template>
                <template v-else-if="column.dataIndex == 'cardStatus'">
                    <a-badge v-if="text" :status="isServing(text)" :text="text" />
                    <span v-else>--</span>
                    <a-tooltip placement="top" color="#fff">
                        <template #title>
                            <span style="color: #000">{{ record.cardRenson }}</span>
                        </template>
                        <img v-if="record.cardStatus == '异常'" class="outlinebeifen" src="@/assets/images/outlinebeifen.png" />
                    </a-tooltip>
                </template>
                <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip>
            </template>
        </ETable>
        <!-- <template #footer>
            <a-button key="back" @click="handleCancel">取消</a-button>
            <a-button key="submit" type="primary" :loading="state.syncRecordLoading" @click="handlerSyncRecordOk">确定</a-button>
        </template> -->
    </YModal>
</template>

<script setup>
import { reactive, watch } from 'vue'
import Http from '@/utils/http.js'
const props = defineProps({
    open: {
        type: Boolean,
        default: false,
    },
    paramsEdit: {
        type: Object,
        default: () => ({}),
    },
})
const emit = defineEmits(['update:open'])
const state = reactive({
    loading: false,
    // 同步记录
    syncRecordLoading: false,
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    dataSource: [],
})

// 同步记录
const syncRecordColumns = [
    { title: '序号', dataIndex: 'index', width: 80 },
    { title: '姓名', dataIndex: 'userName' },
    { title: '设备名称', dataIndex: 'deviceName' },
    { title: '人脸同步状态', dataIndex: 'status' },
    { title: '卡号同步状态', dataIndex: 'cardStatus' },
    { title: '操作人', dataIndex: 'createBy' },
    { title: '下发时间', dataIndex: 'syncTime', width: 140 },
]

// 取消同步记录
const handleCancel = () => {
    emit('update:open', false)
}
// 确定同步记录
const handlerSyncRecordOk = () => {
    handleCancel()
}
const pageSyncLog = () => {
    const params = {
        deviceId: props.paramsEdit.id,
        ...state.pagination,
    }
    state.loading = true
    Http.post('/cloud/faceSync/pageSyncLog', params)
        .then(({ data }) => {
            const { list, pageNo, pageSize, total } = data
            state.dataSource = list || []
            state.pagination.pageNo = pageNo
            state.pagination.pageSize = pageSize
            state.pagination.total = total
        })
        .finally(() => {
            state.loading = false
        })
}

// 分页
const handlesyncRecorTableChange = ({ current, pageSize, total }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    pageSyncLog()
}
const isServing = computed(() => {
    return item => {
        if (item.indexOf('成功') !== -1 || item.indexOf('已绑定') !== -1 || item.indexOf('已同步') !== -1) {
            return 'success'
        }
        if (item.indexOf('中') !== -1) {
            return 'warning'
        }
        return 'error'
    }
})

watch(
    () => props.open,
    val => {
        state.dataSource = []
        val && pageSyncLog()
    },
)
</script>

<style lang="less" scoped>
.outlinebeifen {
    display: inline-block;
    cursor: pointer;
    width: 12px;
    height: 12px;
    padding: 1px;
    border-radius: 50%;
    margin-left: 3px;
    background-color: @error-color;
}
</style>
