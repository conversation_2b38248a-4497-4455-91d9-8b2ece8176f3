<!-- 人脸库 -->
<template>
    <div class="device-face-database">
        <div class="pageHead">
            <div class="pageHead_title">人脸库</div>
        </div>
        <div class="pageBody">
            <div class="device-list">
                <a-input-search allowClear v-model:value.trim="state.nameDevice" placeholder="请输入"
                    @search="searchDevice">
                    <template #addonBefore>
                        <a-select class="input-search" v-model:value.trim="state.searchType">
                            <a-select-option value="no">序列号</a-select-option>
                            <a-select-option value="name">设备名称</a-select-option>
                        </a-select>
                    </template>
                </a-input-search>

                <div class="create-btn device-btns">
                    <a-button :disabled="!state.checkedDeviceList.length" type="primary"
                        v-auth="'canteenMachine.device.faceDatabase.batchAssociation'" @click="handlerOpenControlRef">
                        批量关联用户数据
                    </a-button>
                    <a-button :disabled="!state.checkedDeviceList.length" type="primary"
                        v-auth="'canteenMachine.device.faceDatabase.batchSync'" @click="handlerSynchronization(true)">
                        批量同步
                    </a-button>
                </div>
                <a-spin :spinning="state.deviceLoading" tip="数据加载中..." style="height: 500px">
                    <div class="equipment_list" id="equipment_list" ref="equipmentListRef" @scroll="deviceScroll"
                        v-if="state.treeData.length">
                        <!-- :disabled="!state.online_list.length" -->
                        <a-checkbox style="margin: 0 0 10px 15px" v-model:checked="state.isCheckAll"
                            :indeterminate="state.indeterminate" @change="onCheckAllChange">
                            全选
                        </a-checkbox>
                        <a-checkbox-group v-model:value="state.checkedDeviceList" style="width: 100%">
                            <div class="item" v-for="(item, index) in state.treeData" :key="index"
                                @click="hanbleShe(item, index)" :class="state.deviceActive === index ? 'active' : ''">
                                <div class="left">
                                    <a-checkbox :value="item" :disabled="item.machineStatus !== 1"></a-checkbox>
                                    <div class="on_line" :class="item.machineStatus == 1 ? 'act' : ''"></div>
                                    <a-tooltip placement="top" v-if="item.machineName?.length > 10">
                                        <template #title>
                                            <span>{{ item.machineName }}</span>
                                        </template>
                                        <div class="name"
                                            :style="state.deviceActive == index ? 'color: @primary-color;' : ''">
                                            {{ item.machineName }}
                                        </div>
                                    </a-tooltip>
                                    <div class="name" v-else
                                        :style="state.deviceActive == index ? 'color: @primary-color;' : ''">
                                        {{ item.machineName }}
                                    </div>
                                </div>
                                <div :class="state.deviceActive == index ? 'right right_class ' : 'right'">
                                    <span>
                                        {{ item.synchronousNumber || 0 }}
                                    </span>
                                    <span class="color_black division_line">/</span>
                                    <span class="color_black">
                                        {{ item.faceNumber || 0 }}
                                    </span>
                                </div>
                            </div>
                        </a-checkbox-group>
                        <div class="exactly" v-if="state.treeData.length === state.deviceTotal">已加载到底了~</div>
                    </div>
                    <Empty v-else tips="暂无设备数据" />
                </a-spin>
            </div>
            <div class="device-table">
                <div class="top_name">{{ state.paramsEdit.machineName }}</div>
                <div class="warp">
                    <div class="left">
                        <span>{{ state.paramsEdit.siteName }}</span>
                    </div>
                    <div class="right">
                        <span class="ti">序列号：</span>
                        <span class="xu_num">{{ state.paramsEdit.no }}</span>
                    </div>
                </div>
                <div class="create-btn">
                    <searchForm v-model:formState="state.form" :formList="formList" @submit="queryInitData"
                        @reset="queryInitData" />
                    <a-space>
                        <a-button type="primary" @click="handlerOpenControlRef"
                            v-auth="'canteenMachine.device.faceDatabase.relation'">
                            关联用户数据
                        </a-button>
                        <a-button type="primary" ghost :disabled="state.paramsEdit.machineStatus !== 1"
                            @click="handlerSynchronization(false)"
                            v-auth="'canteenMachine.device.faceDatabase.oneSync'">
                            一键同步
                        </a-button>
                        <a-button type="primary" ghost @click="state.syncRecordOpen = true"
                            v-auth="'canteenMachine.device.faceDatabase.synchronous'">
                            同步记录
                        </a-button>
                        <a-button type="primary" ghost danger @click="handleClearall"
                            v-auth="'canteenMachine.device.faceDatabase.empty'">
                            清空
                        </a-button>
                    </a-space>
                </div>
                <ETable class="reset-table" :columns="columns" :scroll="{ x: 1500 }" :loading="state.loading"
                    :data-source="state.dataSource" :paginations="state.pagination" @change="handleTableChange">
                    <template #headerCell="{ column }">
                        <template v-if="column.dataIndex == 'status'">
                            <a-dropdown :placement="bottomRight">
                                <a style="color: #000" @click.prevent>
                                    人脸同步状态
                                    <caret-down-outlined style="color: var(--primary-color)" />
                                </a>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item v-for="(item, index) in statusList" :key="index"
                                            @click="chooseStatus(item, index, 'status')">
                                            <span class="menu-item-title"
                                                :class="{ active: state.statusObj.statusIndex === index }">
                                                {{ item.text }}
                                            </span>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </template>
                        <template v-else-if="column.dataIndex == 'cardStatus'">
                            <a-dropdown :placement="bottomRight">
                                <a style="color: #000" @click.prevent>
                                    卡号同步状态
                                    <caret-down-outlined style="color: var(--primary-color)" />
                                </a>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item v-for="(item, index) in statusList" :key="index"
                                            @click="chooseStatus(item, index, 'cardStatus')">
                                            <span class="menu-item-title"
                                                :class="{ active: state.statusObj.cardStatusIndex === index }">
                                                {{ item.text }}
                                            </span>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </template>
                        <template v-else-if="column.dataIndex === 'userType'">
                            <a-dropdown :placement="bottomRight">
                                <a style="color: #000" @click.prevent>
                                    人员类型
                                    <caret-down-outlined style="color: var(--primary-color)" />
                                </a>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item v-for="(item, index) in peopleList" :key="index"
                                            @click="chooseStatus(item, index, 'userType')">
                                            <span class="menu-item-title"
                                                :class="{ active: state.statusObj.peopleIndex === index }">
                                                {{ item.label }}
                                            </span>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </template>
                    </template>
                    <template #bodyCell="{ column, text, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                        <template v-else-if="column.dataIndex == 'userType'">
                            {{ record.userType == 1 ? '学生' : record.userType == 2 ? '教师工' : '非本校师生' }}
                        </template>
                        <template v-else-if="column.dataIndex == 'status'">
                            <a-badge :status="badgeComput(record.status)?.status"
                                :text="badgeComput(record.status)?.text"></a-badge>
                            <a-tooltip placement="top" color="#fff">
                                <template #title>
                                    <span style="color: #000">{{ record.renson }}</span>
                                </template>
                                <img v-if="record.status == 1" class="outlinebeifen"
                                    src="@/assets/images/outlinebeifen.png" />
                            </a-tooltip>
                        </template>

                        <template v-else-if="column.dataIndex == 'cardStatus'">
                            <a-badge :status="badgeComput(record.cardStatus)?.status"
                                :text="badgeComput(record.cardStatus)?.text"></a-badge>
                            <a-tooltip placement="top" color="#fff">
                                <template #title>
                                    <span style="color: #000">{{ record.cardRenson }}</span>
                                </template>
                                <img v-if="record.cardStatus == 1" class="outlinebeifen"
                                    src="@/assets/images/outlinebeifen.png" />
                            </a-tooltip>
                        </template>
                        <template v-else-if="column.dataIndex == 'operate'">
                            <a-button type="link" class="btn-link-color"
                                v-auth="'canteenMachine.device.faceDatabase.sync'" @click="handleSync(record, true)">
                                同步
                            </a-button>
                            <a-button type="link" danger class="btn-link-color"
                                v-auth="'canteenMachine.device.faceDatabase.delete'" @click="handleSync(record, false)">
                                删除
                            </a-button>
                        </template>
                        <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip>
                    </template>
                </ETable>
            </div>
            <OneSyncModal v-model:open="state.oneSyncOpen" :paramsEdit="state.paramsEdit" :isBatch="state.isBatch"
                :isSyncDelete="state.isSyncDelete" :checkedDeviceList="state.checkedDeviceList"
                @emitSearchDevice="getDeviceList" />
            <SyncRecordModal v-model:open="state.syncRecordOpen" :paramsEdit="state.paramsEdit" />
        </div>
        <YSelect v-model:visible="state.selectOpen" :tabs="selectTabs" :selected="state.selected" isPickType="all"
            @confirm="handerConfirm" />

        <FaceModel v-model:syncOpen="state.syncOpen" :isSyncDelete="state.isSyncDelete" :paramsEdit="state.paramsEdit"
            @emitSearchDevice="searchDevice" />
    </div>
</template>

<script setup>
import { reactive, onMounted, shallowRef, computed } from 'vue'
import Http from '@/utils/http.js'
import FaceModel from './FaceModel.vue'
import OneSyncModal from './oneSyncModal.vue'
import SyncRecordModal from './syncRecordModal.vue'
const selectTabs = [
    {
        tab: '学生',
        key: 1,
        businessType: 11,
        code: null,
        single: false, // 单选多选
        checked: true,
    },
    {
        tab: '教职工',
        key: 2,
        businessType: 21,
        code: null,
        single: false, // 单选多选
        checked: false,
    },
]
const statusList = [
    { text: '全部', key: null, status: null },
    { text: '已同步', key: 0, status: 'success' },
    { text: '异常', key: 1, status: 'error' },
    { text: '未同步', key: 2, status: 'default' },
    { text: '同步中', key: 3, status: 'warning' },
    { text: '删除中', key: 4, status: 'error' },
]
const badgeComput = computed(() => {
    return status => {
        const item = statusList.find(item => item.key === status)
        if (item) {
            return item
        }
        return {
            text: '--',
            status: 'default',
        }
    }
})
const peopleList = [
    { label: '全部', value: null },
    { label: '学生', value: 1 },
    { label: '教师工', value: 2 },
    { label: '非本校师生', value: 3 },
]
const formList = [
    {
        type: 'input',
        value: 'name',
        label: '',
        span: 12,
        attrs: {
            placeholder: '请输入姓名',
        },
    },
]

const columns = [
    { title: '序号', dataIndex: 'index', width: 80 },
    { title: 'ID', dataIndex: 'id', width: 180 },
    { title: '姓名', dataIndex: 'userName' },
    { title: '人员类型', dataIndex: 'userType' },
    { title: '人脸同步状态', dataIndex: 'status' },
    { title: '卡号同步状态', dataIndex: 'cardStatus' },
    { title: '最近同步人员', dataIndex: 'updateBy' },
    { title: '更新时间', dataIndex: 'updateTime' },
    { title: '操作', dataIndex: 'operate', fixed: 'right', width: 110 },
]
const equipmentListRef = shallowRef()
let isLoading = shallowRef(false)
const state = reactive({
    syncOpen: false,
    isSyncDelete: true,
    treeData: [],
    paramsEdit: {},
    isBatch: false,
    loading: false,
    statusObj: {
        cardStatusIndex: 0,
        statusIndex: 0,
        peopleIndex: 0,
    },
    dataSource: [],
    deviceId: '',
    form: {
        id: '',
        name: '',
        deviceType: 1,
        status: null,
        rensonStatus: null,
        cardStatus: null,
        userType: null,
        machineStatus: null,
    },
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    // 搜索设备tree
    searchType: 'name',
    nameDevice: '',
    online_list: [],
    checkedDeviceList: [],
    isCheckAll: false,
    indeterminate: false,
    deviceLoading: false,
    tableLoading: false,
    deviceTotal: 0, // 设备总数
    deviceActive: 0,
    // 设备列表
    devicePagination: {
        pageNo: 1,
        pageSize: 20,
        total: 0,
    },
    // 选人
    selectOpen: false,
    selected: [],
    // 一键同步
    oneSyncOpen: false,
    // 同步记录
    syncRecordOpen: false,
})

//  * 获取列表数据
const getInitData = async () => {
    try {
        state.loading = true
        const params = {
            ...state.pagination,
            ...state.form,
            id: state.paramsEdit.id,
        }

        await Http.post('cloud/faceSync/getRecordPage', params).then(({ data }) => {
            const { pageNo, pageSize, total, list } = data
            state.dataSource = list
            state.pagination.pageNo = pageNo
            state.pagination.pageSize = pageSize
            state.pagination.total = total
        })
    } catch (error) {
        console.log('getList error: ', error)
    } finally {
        state.loading = false
    }
}

// 分页
const handleTableChange = ({ current, pageSize, total }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    getInitData()
}

// 查询重置
const queryInitData = item => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    if (item == null) {
        state.form.status = null
        state.statusObj.statusIndex = 0
        state.form.cardStatus = null
        state.statusObj.cardStatusIndex = 0
        state.form.userType = null
        state.statusObj.peopleIndex = 0
    }

    getInitData()
}

// 同步
const handleSync = (item = {}, bol) => {
    state.isSyncDelete = bol
    state.paramsEdit._userId = item.userId || ''
    state.syncOpen = true
}
const deviceListNum = ref(0)

watch(
    () => state.checkedDeviceList,
    val => {
        if (val.length) {
            if (val.length == deviceListNum.value) {
                state.isCheckAll = true
                state.indeterminate = false
            } else {
                state.indeterminate = true
                state.isCheckAll = false
            }
        } else {
            state.isCheckAll = false
            state.indeterminate = false
        }
    },
    {
        deep: true,
    },
)
// 切换设备列表
const hanbleShe = (item, index) => {
    state.deviceActive = index
    state.form.id = item.id
    state.deviceId = item.id
    state.form.machineStatus = item.machineStatus
    state.pagination.pageNo = 1
    state.paramsEdit = item
    getInitData()
}

// 全选
const onCheckAllChange = e => {
    let _checkedDeviceList = []
    state.treeData.forEach(item => {
        if (item.machineStatus == 1) {
            _checkedDeviceList.push(item)
        }
    })
    Object.assign(state, {
        checkedDeviceList: e.target.checked ? _checkedDeviceList : [],
        indeterminate: !(state.treeData.length === _checkedDeviceList.length),
    })
}

// 获取设备列表
const getDeviceList = bol => {
    const params = {
        name: '',
        deviceTypeList: [35],
        passsceneId: '',
        [state.searchType]: state.nameDevice,
        ...state.devicePagination,
    }
    deviceListNum.value = 0
    Http.post('/cloud/v2/machine/page', params).then(({ data }) => {
        const { pageNo, pageSize, total, list } = data
        state.devicePagination.pageNo = pageNo
        state.devicePagination.pageSize = pageSize
        state.deviceTotal = total
        if (bol) {
            state.treeData = state.treeData.concat(list)
        } else {
            state.treeData = list
        }
        isLoading.value = false // 无数据可以加载
        state.form.id = list[0]?.id || ''
        state.deviceId = list[0]?.id || ''
        state.form.machineStatus = list[0]?.machineStatus || ''
        state.paramsEdit = list[0] || {}
        state.deviceActive = 0
        state.treeData.forEach(item => {
            if (item.machineStatus == 1) {
                deviceListNum.value++
            }
        })
        state.isCheckAll = false
        getInitData()
    })
}
// 滚动加载设备列表
const deviceScroll = async () => {
    isLoading.value = true // 是否有数据可以加载
    const dom = document.getElementById('equipment_list')
    const bottomOfWindow = dom.scrollHeight - dom.scrollTop - dom.clientHeight <= 100
    const { pageSize, pageNo } = state.devicePagination
    if (bottomOfWindow && isLoading.value && pageSize * pageNo < state.deviceTotal) {
        state.devicePagination.pageNo = pageNo + 1 // 每次分页+1
        getDeviceList(true)
    }
}

// 搜索设备列表
const searchDevice = () => {
    state.devicePagination.pageSize = 20
    state.devicePagination.pageNo = 1
    state.treeData = []
    state.indeterminate = false
    state.isCheckAll = false
    state.checkedDeviceList = []
    getDeviceList()
}

// 设备列表更加类型搜索
watch(
    () => state.searchType,
    (val, oldVal) => {
        // 切换类型后 滚动条回到顶部
        if (equipmentListRef.value) {
            equipmentListRef.value.scrollTop = 0
        }

        if (val !== oldVal && state.nameDevice) {
            state.nameDevice = ''
            searchDevice()
        }
    },
)
// 开启选人弹框
const handlerOpenControlRef = bol => {
    state.selectOpen = true
    state.selected = []
}
// 通通是学生的数据
const identitys = ['school', 'campus', 'academics', 'grade', 'classes', 'student']
const handerConfirm = data => {
    state.selected = data
    const personListDTO = data.map(v => {
        return {
            id: v.id,
            name: v.showName || v.name,
            typeValue: v.rollValue || v.typeValue,
            identity: identitys.includes(v.rollValue) || identitys.includes(v.typeValue) ? 0 : 1,
        }
    })
    let params = {
        deviceId: state.form.id || state.deviceId,
        personListDTO,
    }
    if (state.checkedDeviceList.length) {
        params.deviceId = ''
        params.deviceIds = state.checkedDeviceList.map(v => v.id)
    }
    Http.post('/cloud/faceDevice/V3/bindFaceInfo', params).then(({ message }) => {
        YMessage.success(message)
        state.selectOpen = false
        state.isCheckAll = false
        state.checkedDeviceList = []
        getInitData()
    })
}
// 批量同步数据
const handlerSynchronization = bol => {
    state.oneSyncOpen = true
    state.isBatch = bol
}

// 清空
const handleClearall = () => {
    yConfirm('提示', '确定清空本机所有的人员信息？', '确 认', '取 消').then(res => {
        res &&
            Http.post('/cloud/faceSync/sync/clear', { deviceId: state.form.id || state.deviceId }).then(({ message }) => {
                YMessage.success(message)
                getInitData()
            })
    })
}

// 切换状态
const chooseStatus = (item, index, type) => {
    state.pagination.pageNo = 1
    if (type === 'cardStatus') {
        // 卡号同步状态
        state.form.cardStatus = item.key
        state.statusObj.cardStatusIndex = index
    } else if (type === 'status') {
        // 人脸同步状态
        state.form.status = item.key
        state.statusObj.statusIndex = index
    } else {
        // 选择人员类型
        state.form.userType = item.value
        state.statusObj.peopleIndex = index
    }
    getInitData()
}

const isServing = computed(() => {
    return item => {
        if (item.indexOf('已绑定') !== -1 || item.indexOf('已同步') !== -1) {
            return 'success'
        }
        if (item.indexOf('中') !== -1) {
            return 'warning'
        }
        if (item.indexOf('未') !== -1) {
            return 'default'
        }
        return 'error'
    }
})
onMounted(() => {
    getDeviceList()
})
</script>

<style lang="less" scoped>
.device-face-database {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;
        }
    }

    .pageBody {
        display: flex;
        justify-content: space-between;
        height: calc(100vh - 160px);

        .device-list {
            width: 290px;
            padding: 24px 10px 0;
            border-right: 1px solid @border-color-base;

            .input-search {
                width: 90px;

                :deep(.ant-select-selector) {
                    padding: 0;
                    padding-right: 5px;
                }
            }

            .device-btns {
                display: flex;
                justify-content: space-between;
            }
        }

        .create-btn {
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
        }

        .device-table {
            flex: 1;
            padding: 20px;

            .top_name {
                font-size: 18px;
                font-weight: 600;
                color: @heading-color;
                line-height: 25px;
                margin-bottom: 8px;
            }

            .warp {
                margin-bottom: 22px;
                display: flex;

                .left {
                    margin-right: 24px;
                }

                .right {
                    .ti {
                        color: @disabled-color;
                    }
                }
            }
        }

        .reset-table {
            width: calc(100vw - 564px);
        }
    }

    .equipment_list {
        margin-top: 10px;
        max-height: 100%;
        overflow-y: auto;
        height: calc(100vh - 270px);

        .item {
            padding: 12px 16px;
            border-bottom: 1px solid @border-color-base;
            width: 100%;
            height: 52px;
            line-height: 52px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;

            .left {
                display: flex;
                align-items: center;
                justify-content: space-between;
                max-width: 60%;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;

                .on_line {
                    width: 10px;
                    height: 8px;
                    border-radius: 50%;
                    background-color: #bfbfbf;
                    margin: 0 6px;
                }

                .name {
                    font-size: 14px;
                    font-family:
                        PingFangSC-Semibold,
                        PingFang SC;
                    font-weight: 600;
                    line-height: 20px;
                    width: 157px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }

                .act {
                    background-color: @primary-color;
                }
            }

            .right {
                color: #ccc;
                font-size: 14px;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                display: flex;
                line-height: 20px;
                color: @heading-color;

                .division_line {
                    padding: 0px 2px;
                }

                .color_black,
                .division_line {
                    color: @text-color-secondary;
                }
            }

            .right_class {

                .division_line,
                .color_black,
                span {
                    color: @primary-color !important;
                }
            }
        }

        .active {
            background: @acitve-background;
            color: @primary-color;
        }

        .exactly {
            text-align: center;
            font-size: 13px;
            margin-top: 20px;
            width: 100%;
            color: @border-color-base;
        }
    }

    .outlinebeifen {
        display: inline-block;
        cursor: pointer;
        width: 12px;
        height: 12px;
        padding: 1px;
        border-radius: 50%;
        margin-left: 3px;
        background-color: @error-color;
    }
}

.menu-item-title {
    &.active {
        color: @primary-color;
    }
}
</style>
