<template>
    <YModal v-model:open="props.open" title="一键同步" :bodyStyle="{ padding: '24px' }" @cancel="handleCancel">
        <a-form :model="state.oneSyncForm" ref="oneSyncRef" layout="vertical">
            <a-form-item label="同步类型：" name="syncTypes" :rules="[{ required: true, message: '请选择同步类型' }]">
                <a-checkbox-group v-model:value="state.oneSyncForm.syncTypes" style="width: 100%">
                    <a-checkbox value="FACE">人脸</a-checkbox>
                    <a-checkbox value="CARD">卡号</a-checkbox>
                </a-checkbox-group>
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button key="back" @click="handleCancel">取消</a-button>
            <a-button key="submit" type="primary" :loading="state.oneSyncLoading" @click="handlerSyncOk">确定</a-button>
        </template>
    </YModal>
</template>

<script setup>
import { reactive } from 'vue'
import Http from '@/utils/http.js'
const props = defineProps({
    open: {
        type: <PERSON><PERSON>an,
        default: false,
    },
    paramsEdit: {
        type: Object,
        default: () => ({}),
    },
    checkedDeviceList: {
        type: Array,
        default: () => [],
    },
    isBatch: {
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['update:open', 'emitSearchDevice'])
const oneSyncRef = shallowRef()
const state = reactive({
    // 一键同步
    oneSyncLoading: false,
    oneSyncForm: {
        syncTypes: ['FACE', 'CARD'],
    },
})

// 取消一键同步
const handleCancel = () => {
    oneSyncRef.value.resetFields()
    emit('update:open', false)
}
// 确定一键同步
const handlerSyncOk = () => {
    oneSyncRef.value.validate().then(res => {
        state.oneSyncLoading = true
        let params = {
            deviceId: props.paramsEdit.id,
            ...state.oneSyncForm,
        }
        if (props.isBatch) {
            params.deviceId = ''
            params.deviceIds = props.checkedDeviceList.map(v => v.id)
        }
        Http.post('/cloud/faceSync/sync', params)
            .then(({ message }) => {
                YMessage.success(message)
                emit('emitSearchDevice')
                handleCancel()
            })
            .finally(() => {
                state.oneSyncLoading = false
            })
    })
}
</script>

<style lang="less" scoped></style>
