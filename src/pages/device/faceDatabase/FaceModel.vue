<template>
    <YModal v-model:open="props.syncOpen" :title="props.isSyncDelete ? '同步' : '刪除'" @ok="handleOk" @cancel="handleSyncCancel">
        <div class="face-model">
            <a-spin :spinning="state.spinning">
                <div style="margin-bottom: 10px">{{ props.isSyncDelete ? '同步' : '刪除' }}人员信息？</div>
                <a-form v-if="props.isSyncDelete" :model="state.oneSyncForm" ref="oneSyncRef">
                    <a-form-item label="同步类型：" name="syncTypes" :rules="[{ required: true, message: '请选择同步类型' }]">
                        <a-checkbox-group v-model:value="state.oneSyncForm.syncTypes" style="width: 100%">
                            <a-row>
                                <a-col :span="8">
                                    <a-checkbox value="FACE">人脸</a-checkbox>
                                </a-col>
                                <a-col :span="8">
                                    <a-checkbox value="CARD">卡号</a-checkbox>
                                </a-col>
                            </a-row>
                        </a-checkbox-group>
                    </a-form-item>
                </a-form>
                <div class="text">当前设备</div>
                <a-checkbox v-model:checked="state.checked">
                    <div class="curr">
                        <div class="right">
                            <div class="top">
                                <div class="left" :style="paramsEdit.machineStatus == 1 ? 'color: #00b781' : ''">
                                    <span class="cyl" :class="['act_no', 'act'][Number(paramsEdit.machineStatus)]"></span>
                                    <span>{{ paramsEdit.machineName }}</span>
                                </div>
                                <div :class="paramsEdit.machineStatus == 1 ? 'right right_class ' : 'right'">
                                    <span>{{ paramsEdit.synchronousNumber }}</span>
                                    <span class="color_black division_line">/</span>
                                    <span class="color_black">{{ paramsEdit.faceNumber }}</span>
                                </div>
                            </div>
                            <div class="down" :style="paramsEdit.machineStatus == 1 ? 'color: #00b781' : ''">
                                <span>{{ paramsEdit.siteName }}</span>
                            </div>
                        </div>
                    </div>
                </a-checkbox>
                <!-- v-if="state.plainOptions && state.plainOptions.length > 0" -->
                <div v-if="state.plainOptions && state.plainOptions.length > 0">
                    <div class="tip">以下设备存在相同人员，可选择同时更新</div>
                    <div style="margin-bottom: 20px">
                        <a-checkbox
                            v-model:checked="state.checkAll"
                            :indeterminate="state.indeterminate"
                            @change="onCheckAllChange"
                        >
                            全选
                        </a-checkbox>
                    </div>
                    <div class="check_box">
                        <a-checkbox-group v-model:value="state.checkedList">
                            <a-checkbox :value="item" v-for="(item, index) in state.plainOptions" :key="index">
                                <div class="curr_item">
                                    <div class="right">
                                        <div class="top">
                                            <div class="left">
                                                <span class="cyl" :class="['act_no', 'act'][Number(item.machineStatus)]"></span>
                                                <span>{{ item.machineName }}</span>
                                            </div>
                                            <div class="right">
                                                <span>
                                                    {{ item.synchronousNumber || 0 }}
                                                </span>
                                                <span class="color_black division_line">/</span>
                                                <span class="color_black">
                                                    {{ item.faceNumber || 0 }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="down">
                                            <span>{{ item.siteName }}</span>
                                        </div>
                                    </div>
                                </div>
                            </a-checkbox>
                        </a-checkbox-group>
                    </div>
                </div>
            </a-spin>
        </div>
    </YModal>
</template>

<script setup>
import { reactive } from 'vue'
import Http from '@/utils/http.js'
const oneSyncRef = shallowRef()
const props = defineProps({
    syncOpen: {
        type: Boolean,
        default: false,
    },
    isSyncDelete: {
        type: Boolean,
        default: true,
    },
    paramsEdit: {
        type: Object,
        default: () => ({
            machineStatus: 0,
            synchronousNumber: 0,
            faceNumber: 0,
            machineName: '',
            siteName: '',
        }),
    },
})
const state = reactive({
    spinning: false,
    checked: true,

    // 多选----
    checkAll: false,
    checkedList: [],
    plainOptions: [],
    indeterminate: false,
    // 多选
    pagination: {
        pageNo: 1,
        pageSize: 100,
        total: 0,
    },
    oneSyncForm: {
        syncTypes: ['FACE', 'CARD'],
    },
})

const emit = defineEmits(['syncOnePerson', 'update:syncOpen', 'emitSearchDevice'])
const handleSyncCancel = () => {
    emit('update:syncOpen', false)
}
const init = () => {
    const { _userId, id } = props.paramsEdit
    const params = {
        deviceId: '',
        deviceIds: [],
        deviceType: 1,
        userIds: [_userId],
        ...state.oneSyncForm,
    }
    if (state.checked) {
        params.deviceId = id
    }
    let API = props.isSyncDelete ? '/cloud/faceSync/sync' : '/cloud/faceSync/sync/clear'
    Http.post(API, params).then(({ message }) => {
        YMessage.success(message)
        handleSyncCancel()
        emit('emitSearchDevice')
    })
}
// 同步单个人
function handleOk() {
    if (props.isSyncDelete) {
        oneSyncRef.value.validate().then(res => {
            init()
        })
    } else {
        init()
    }
}

function onCheckAllChange(e) {
    Object.assign(state, {
        checkedList: e.target.checked ? state.plainOptions : [],
        indeterminate: false,
    })
}
// 选择框
watch(
    () => state.checkedList,
    val => {
        state.indeterminate = !!val.length && val.length < state.plainOptions.length
        state.checkAll = val.length === state.plainOptions.length
    },
)
watch(
    () => props.syncOpen,
    val => {
        if (val) {
            state.oneSyncForm.syncTypes = ['FACE', 'CARD']
        }
    },
)
</script>

<style lang="less" scoped>
.face-model {
    padding: 20px;

    .text {
        font-size: 14px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        margin-top: 20px;
        margin-bottom: 12px;
    }
    .curr {
        display: flex;
        align-items: center;
        padding: 12px;
        color: #00b781;
        width: 100%;
        height: 72px;
        border-radius: 4px;
        background: rgba(0, 183, 129, 0.08);
        .left {
            margin-right: 14px;
        }
        .right {
            .top {
                width: 420px;
                margin-bottom: 10px;
                display: flex;
                justify-content: space-between;
                .left {
                    max-width: 70%;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                    font-size: 14px;
                    font-family:
                        PingFangSC-Semibold,
                        PingFang SC;
                    font-weight: 600;
                    line-height: 20px;
                    color: rgba(0, 0, 0, 0.85);
                    .cyl {
                        margin-right: 5px;
                        display: inline-block;
                        width: 8px;
                        height: 8px;
                        border-radius: 4px;
                    }
                    .act_no {
                        background-color: #bfbfbf;
                    }
                    .act {
                        background-color: #00b781;
                    }
                }
                .right {
                    color: rgba(0, 0, 0, 0.85);
                    flex: 1;
                    text-align: right;
                    .division_line {
                        padding: 0px 2px;
                    }
                    .color_black,
                    .division_line {
                        color: rgba(0, 0, 0, 0.45);
                    }
                }
                .right_class {
                    .division_line,
                    .color_black,
                    span {
                        color: #00b781 !important;
                    }
                }
            }
            .down {
                font-size: 14px;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.45);
                line-height: 20px;
            }
        }
        :deep(.ant-checkbox) {
            top: 20px;
        }
    }
    :deep(.ant-checkbox-wrapper) {
        margin: 0 !important;
        width: 100% !important;
    }
    .curr_item {
        display: flex;
        align-items: center;
        padding: 12px;
        // color: #00b781;
        width: 440px;
        height: 72px;
        border-radius: 4px;
        background: #f6f6f6;
        margin-bottom: 10px;
        .right {
            width: 100%;
            .top {
                width: 100%;
                margin-bottom: 10px;
                display: flex;
                justify-content: space-between;
                .left {
                    max-width: 70%;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.85);
                    font-family:
                        PingFangSC-Semibold,
                        PingFang SC;
                    font-weight: 600;
                    line-height: 20px;
                    .cyl {
                        margin-right: 5px;
                        display: inline-block;
                        width: 8px;
                        height: 8px;
                        border-radius: 4px;
                        background-color: #00b781;
                    }
                    .act_no {
                        background-color: #bfbfbf;
                    }
                    .act {
                        background-color: #00b781;
                    }
                }
                .right {
                    flex: 1;
                    text-align: right;
                    color: rgba(0, 0, 0, 0.85);
                    .division_line {
                        padding: 0px 2px;
                    }
                    .color_black,
                    .division_line {
                        color: rgba(0, 0, 0, 0.45);
                    }
                }
                .right_class {
                    .division_line,
                    .color_black,
                    span {
                        color: #00b781 !important;
                    }
                }
            }
            .down {
                font-size: 14px;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.45);
                line-height: 20px;
            }
        }
        :deep(.ant-checkbox) {
            top: 20px;
        }
    }
    .tip {
        margin: 20px 0;
    }
    .check_box {
        max-height: 280px;
        overflow-y: auto;
        overflow-x: hidden;
    }
}
</style>
