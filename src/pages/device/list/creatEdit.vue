<template>
    <YDrawer
        :width="500"
        v-model:open="props.deviceOpen"
        :mask="true"
        :title="state.formState.id ? '编辑设备' : '新增设备'"
        @close="cancel"
        getContainer="body"
        :rootStyle="{
            height: '100vh',
            top: '0px',
        }"
    >
        <div class="create-form">
            <a-form :model="state.formState" ref="formRef" layout="vertical" :rules="merchantFormRule">
                <a-form-item label="设备名称：" name="machineName">
                    <a-input v-model:value.trim="state.formState.machineName" show-count :maxlength="20" placeholder="请输入" />
                </a-form-item>
                <a-form-item label="设备型号：" name="deviceManufacturer">
                    <a-select
                        v-model:value="state.formState.deviceManufacturer"
                        style="width: 100%"
                        :disabled="!!state.formState.id"
                        placeholder="请选择"
                        @change="handlerModelOptions"
                    >
                        <a-select-option
                            v-for="it in state.modelOptions"
                            :key="it.deviceManufacturer"
                            :value="it.deviceManufacturer"
                        >
                            {{ it.deviceManufacturer }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="选择场地：" name="siteId">
                    <a-tree-select
                        style="width: 100%"
                        v-model:value="state.formState.siteId"
                        :tree-data="state.siteOptions"
                        :fieldNames="fieldNames"
                        :disabled="!!state.formState.id"
                        placeholder="请选择"
                        allow-clear
                        show-search
                        treeNodeFilterProp="name"
                    ></a-tree-select>
                </a-form-item>

                <a-form-item v-if="state.formState.id" label="设备序列号：" name="no">
                    <a-input v-model:value.trim="state.formState.no" :disabled="!!state.formState.id" placeholder="请选择" />
                </a-form-item>
                <a-form-item v-else label="设备数量：" name="machineNumber">
                    <a-input-number v-model:value.trim="state.formState.machineNumber" placeholder="请输入" />
                </a-form-item>

                <a-form-item label="管理员：" name="administratorName">
                    <a-input
                        v-model:value.trim="state.formState.administratorName"
                        readoly
                        @click="() => (state.selectOpen = true)"
                        placeholder="请选择"
                    />
                </a-form-item>
            </a-form>
        </div>
        <template #footer>
            <a-button @click="cancel">取消</a-button>
            <a-button type="primary" :loading="state.loading" @click="submit">确定</a-button>
        </template>
    </YDrawer>

    <YSelect
        v-model:visible="state.selectOpen"
        :tabs="selectTabs"
        :selected="state.selected"
        :maximum="10"
        :isPickType="'people_dept'"
        @confirm="handerConfirm"
    />
</template>

<script setup>
const props = defineProps({
    deviceOpen: {
        type: Boolean,
        default: false,
    },
    paramsEdit: {
        type: Object,
        default: () => ({}),
    },
})
const selectTabs = [
    {
        tab: '教职工',
        key: 2,
        businessType: 21,
        code: null,
        single: false, // 单选多选
        checked: true,
    },
]
const emit = defineEmits(['emitInitData', 'update:deviceOpen'])

const fieldNames = {
    label: 'name',
    value: 'id',
    children: 'children',
}
const state = reactive({
    selected: [],
    selectOpen: false,
    siteOptions: [],
    modelOptions: [],
    phoneModelOptions: [],
    formState: {
        machineName: '',
        machineNumber: 1,
        administratorName: '',
        teacherId: [],
        deviceManufacturer: null,
        deviceType: 35,
        deviceMode: 1,
        no: '',
        id: '',
        deviceMode: null,
        siteId: [],
        siteName: '',
        firstDeviceId: '',
        otherDeviceIdList: [],
    },
    loading: false,
})

const merchantFormRule = {
    machineName: [{ required: true, message: '请输入' }],
    no: [{ required: false, message: '请输入' }],
    deviceManufacturer: [{ required: true, message: '请选择' }],
    firstDeviceId: [{ required: true, message: '请选择' }],
    siteId: [{ required: true, message: '请选择' }],
}
const handlerModelOptions = value => {
    state.modelOptions.forEach(v => {
        if (v.deviceManufacturer == value) {
            state.formState.deviceMode = v.deviceMode
        }
    })
}

const formRef = ref(null)
const cancel = () => {
    // state.formState = {}
    state.formState.id = ''
    formRef.value.resetFields()
    emit('update:deviceOpen', false)
}
// 设备提交编辑
const submit = () => {
    formRef.value
        .validate()
        .then(() => {
            let API = '/cloud/v2/machine/create'
            state.loading = true
            state.formState.deviceType = 35
            if (state.formState.id) {
                API = '/cloud/v2/machine/update'
            }
            http.post(API, state.formState)
                .then(({ message }) => {
                    YMessage.success(message)
                    emit('emitInitData')
                    cancel()
                })
                .finally(() => (state.loading = false))
        })
        .catch(err => {
            console.log('err:', err)
        })
}

// 设备场地
const getNoSignSite = async () => {
    http.get('/cloud/v2/machine/site/list', { type: 2 }).then(({ data }) => {
        state.siteOptions = data.map(v => {
            return {
                ...v,
                disabled: true,
            }
        })
    })
}

// 设备型号
const getMachineSite = async () => {
    http.get('/cloud/machine/type/list').then(({ data }) => {
        state.modelOptions = []
        data?.forEach(v => {
            if (v.equipmentType == 35) {
                state.modelOptions = v.deviceBrandDTOList
            }
        })
    })
}
// 设备列表
const getPhoneMachineSite = async () => {
    await http.post('/cloud/v2/machine/list', { type: 35 }).then(({ data }) => {
        state.phoneModelOptions = data
    })
}
// 获取详情
const getAlarmDiteas = () => {
    http.get('/cloud/v2/machine/detail', { id: props.paramsEdit.id }).then(({ data }) => {
        state.formState = data
        state.formState.teacherId = data.administratorList.map(v => v.teacherId)
    })
}

const handerConfirm = data => {
    state.selected = data
    let personName = []
    state.formState.teacherId = []
    debugger
    data?.forEach(v => {
        personName.push(v.name)
        state.formState.teacherId.push(v.id)
    })
    state.formState.administratorName = personName.join(',') || ''
    state.selectOpen = false
}
watch(
    () => props.deviceOpen,
    val => {
        if (val) {
            props.paramsEdit?.id && getAlarmDiteas()
        }
    },
    {
        deep: true,
    },
)
onMounted(() => {
    getNoSignSite()
    getMachineSite()
    getPhoneMachineSite()
})
</script>

<style lang="less" scoped>
.create-form {
    :deep(.ant-form-item) {
        margin-bottom: 20px;
    }
}
</style>
