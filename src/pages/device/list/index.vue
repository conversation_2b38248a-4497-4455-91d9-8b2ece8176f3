<!-- 设备列表 -->
<template>
    <div class="device-list">
        <div class="pageHead">
            <div class="pageHead_title">设备列表</div>
        </div>
        <div class="pageBody">
            <searchForm
                v-model:formState="state.form"
                :formList="formList"
                @submit="queryInitData"
                @reset="queryInitData"
                mb-20
            />
            <div class="create-btn">
                <a-button type="primary" v-auth="'canteenMachine.device.deviceList.add'" @click="handleAddEdit(null)">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建设备
                </a-button>
                <a-button @click="handleRestart">定时重启</a-button>
            </div>
            <ETable
                :columns="columns"
                :minH="450"
                :scroll="{ x: 1500 }"
                :data-source="state.dataSource"
                :paginations="state.pagination"
                :loading="state.loading"
                @change="handleTableChange"
            >
                <template #bodyCell="{ column, text, record, index }">
                    <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                    <template v-else-if="column.dataIndex == 'machineStatus'">
                        {{ machineStatus.find(item => item.value == record.machineStatus)?.name || '--' }}
                    </template>

                    <template v-else-if="column.dataIndex == 'operate'">
                        <a-button
                            type="link"
                            class="btn-link-color"
                            v-auth="'canteenMachine.device.deviceList.edit'"
                            @click="handleAddEdit(record)"
                        >
                            编辑
                        </a-button>
                        <a-button
                            v-if="record.machineStatus == 1"
                            type="link"
                            class="btn-link-color"
                            v-auth="'canteenMachine.device.deviceList.restart'"
                            @click="handleReboot(record.no)"
                        >
                            重启设备
                        </a-button>
                        <a-button
                            type="link"
                            class="btn-link-color"
                            v-auth="'canteenMachine.device.deviceList.update'"
                            @click="handleUpdate(record.no)"
                        >
                            更新软件
                        </a-button>
                        <a-button
                            type="link"
                            danger
                            class="btn-link-color"
                            v-auth="'canteenMachine.device.deviceList.delete'"
                            @click="handleDelete(record.id)"
                        >
                            删除
                        </a-button>
                    </template>
                    <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip>
                </template>
            </ETable>
        </div>
        <CreatEdit v-model:deviceOpen="state.deviceOpen" :paramsEdit="state.paramsEdit" @emitInitData="getInitData" />
        <RestartModal ref="restartModalRef" />
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import Http from '@/utils/http.js'
import CreatEdit from './creatEdit.vue'
import RestartModal from './restartModal.vue'
const restartModalRef = ref(null)
const machineStatus = [
    { label: '全部', name: '--', value: null },
    { label: '在线', name: '在线', value: 1 },
    { label: '离线', name: '离线', value: 0 },
]
const formList = [
    {
        type: 'input',
        value: 'name',
        label: '设备名称',
    },
    {
        type: 'input',
        value: 'no',
        label: '设备序列号',
    },
    {
        type: 'select',
        value: 'machineStatus',
        label: '状态',
        list: machineStatus,
        span: 3,
    },
    {
        type: 'rangePicker',
        value: ['startDate', 'endDate'],
        label: '更新时间',
        attrs: {
            valueFormat: 'YYYY-MM-DD',
        },
    },
]
const columns = [
    { title: '序号', dataIndex: 'index', key: 'index', width: 80 },
    { title: '设备名称', dataIndex: 'machineName', key: 'machineName' },
    { title: '设备型号', dataIndex: 'deviceManufacturer', key: 'deviceManufacturer' },
    { title: '设备序列号', dataIndex: 'no', key: 'no' },
    { title: '所在场地', dataIndex: 'siteName', key: 'siteName' },
    { title: '软件版本号', dataIndex: 'softVersion', key: 'softVersion' },
    { title: '设备状态', dataIndex: 'machineStatus', key: 'machineStatus' },
    { title: '管理员', dataIndex: 'administratorName', key: 'administratorName' },
    { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime' },
    { title: '操作', dataIndex: 'operate', key: 'operate', width: 260, fixed: 'right' },
]

const state = reactive({
    deviceOpen: false,
    deviceType: 'device',
    paramsEdit: {},
    selecteds: [],
    loading: false,
    dataSource: [],
    form: {
        startDate: '',
        endDate: '',
        machineStatus: null,
        no: '',
        name: '',
        deviceType: 35,
    },
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
})

const getInitData = async () => {
    try {
        state.loading = true
        const params = {
            ...state.pagination,
            ...state.form,
        }

        await Http.post('/cloud/v2/machine/page', params).then(({ data }) => {
            const { pageNo, pageSize, total, list } = data
            state.dataSource = list
            state.pagination.pageNo = pageNo
            state.pagination.pageSize = pageSize
            state.pagination.total = total
        })
    } catch (error) {
        console.log('getList error: ', error)
    } finally {
        state.loading = false
    }
}

// 分页管理话机
const handleTableChange = ({ current, pageSize, total }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    getInitData()
}

// 查询重置
const queryInitData = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getInitData()
}

// 新建编辑设备
const handleAddEdit = (item = {}) => {
    state.paramsEdit = item
    state.deviceOpen = true
}
// 重启设备
const handleReboot = deviceNo => {
    Http.post('/cloud/v2/equipment/device/restart', { deviceNo }).then(({ message }) => {
        YMessage.success(message)
        getInitData()
    })
}
// 更新软件
const handleUpdate = deviceNo => {
    Http.post('/cloud/v2/equipment/device/updateAppVersion', { deviceNo }).then(({ message }) => {
        YMessage.success(message)
        getInitData()
    })
}

// 删除设备
const handleDelete = id => {
    yConfirm('删除提示', '删除后不可恢复，确认删除已选设备？', '确 认', '取 消').then(res => {
        res &&
            Http.post('/cloud/v2/equipment/delete', { id }).then(({ message }) => {
                YMessage.success(message)
                getInitData()
            })
    })
}

const handleRestart = () => {
    restartModalRef.value.showeModal()
}

onMounted(() => {
    getInitData()
})
</script>

<style lang="less" scoped>
.device-list {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;
        }
    }

    .pageBody {
        padding: 20px;

        .create-btn {
            text-align: right;
            margin-bottom: 20px;
        }
    }
}
</style>
