<template>
    <YDrawer
        :width="500"
        v-model:open="state.open"
        :mask="true"
        title="定时重启"
        @close="cancel"
        getContainer="body"
        :rootStyle="{
            height: '100vh',
            top: '0px',
        }"
    >
        <div class="restart-form">
            <div class="time-input-section">
                <div class="label">定时重启时间（按照每周为单位循环）</div>
                <a-time-picker
                    v-model:value="state.inputTime"
                    format="HH:mm"
                    value-format="HH:mm"
                    placeholder="请选择时间"
                    style="width: 100%"
                />
                <a-checkbox v-model:checked="state.applyToAll" style="margin-top: 10px">应用到每天</a-checkbox>
            </div>

            <div class="week-time-table">
                <div class="table-header">
                    <div class="col-week">星期</div>
                    <div class="col-time">重启时间</div>
                </div>
                <div class="table-body">
                    <div v-for="(item, index) in state.weekList" :key="index" class="table-row">
                        <div class="col-week">{{ item.label }}</div>
                        <div class="col-time">
                            <a-time-picker
                                v-model:value="item.restartTime"
                                format="HH:mm"
                                value-format="HH:mm"
                                placeholder="重启时间"
                                :allowClear="false"
                            />
                        </div>
                        <div class="col-action">
                            <a-switch v-model:checked="item.status" checked-children="开" un-checked-children="关" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="tip-message">
                <ExclamationCircleOutlined style="color: #faad14; margin-right: 8px" />
                <span>提示：需设备在线才能设定重启成功</span>
            </div>
        </div>
        <template #footer>
            <a-button @click="cancel">取消</a-button>
            <a-button type="primary" :loading="state.loading" @click="submit">确定</a-button>
        </template>
    </YDrawer>
</template>

<script setup>
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import Http from '@/utils/http.js'

// 固定的星期配置，确保无论接口返回多少条都能渲染完整的星期信息
const baseWeekList = [
    { label: '星期一', week: 1 },
    { label: '星期二', week: 2 },
    { label: '星期三', week: 3 },
    { label: '星期四', week: 4 },
    { label: '星期五', week: 5 },
    { label: '星期六', week: 6 },
    { label: '星期日', week: 7 },
]

// 工具方法：创建一份全新的星期列表，避免直接复用引用导致的状态污染
const createWeekList = () =>
    baseWeekList.map(item => ({
        ...item,
        restartTime: null,
        status: false,
    }))

const state = reactive({
    open: false,
    loading: false,
    inputTime: '',
    applyToAll: false,
    weekList: createWeekList(),
})

// 监听应用到每天的复选框
watch(
    () => state.applyToAll,
    val => {
        if (val && state.inputTime) {
            state.weekList.forEach(item => {
                item.restartTime = state.inputTime
            })
        }
    },
)

// 监听输入时间变化
watch(
    () => state.inputTime,
    val => {
        if (val && state.applyToAll) {
            state.weekList.forEach(item => {
                item.restartTime = val
            })
        }
    },
)

const cancel = () => {
    state.open = false
    resetForm()
}

const resetForm = () => {
    state.inputTime = ''
    state.applyToAll = false
    state.weekList = createWeekList()
}

const submit = async () => {
    // 验证至少有一天设置了时间
    const hasTime = state.weekList.some(item => item.restartTime)
    if (!hasTime) {
        YMessage.warning('请至少设置一天的重启时间')
        return
    }

    state.loading = true
    try {
        // 这里调用你的API保存定时重启设置
        const params = state.weekList
            .filter(item => item.restartTime)
            .map(item => ({
                week: item.week,
                restartTime: item.restartTime,
                status: item.status ? 1 : 0,
            }))
        await Http.post('/cloud/schoolMachineRestart/save', { list: params })

        YMessage.success('保存成功')
        state.open = false
        resetForm()
    } catch (error) {
        console.error(error)
    } finally {
        state.loading = false
    }
}

const showeModal = () => {
    state.open = true
    state.inputTime = null
    state.applyToAll = false
    getList()
}

// 获取定时重启列表
const getList = async () => {
    const { data } = await Http.post('/cloud/schoolMachineRestart/list', {})
    const serverList = Array.isArray(data) ? data : []

    // 通过 week 建立映射，方便与固定的星期模板合并
    const weekMap = serverList.reduce((acc, cur) => {
        acc[cur.week] = cur
        return acc
    }, {})

    state.weekList = baseWeekList.map(item => {
        const target = weekMap[item.week] || {}
        return {
            ...item,
            restartTime: target.restartTime || null,
            status: target.status === 1,
            id: target.id,
            schoolId: target.schoolId,
        }
    })
}
defineExpose({
    showeModal,
})
</script>

<style lang="less" scoped>
.restart-form {
    padding: 20px 0;

    .time-input-section {
        margin-bottom: 30px;

        .label {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: 8px;
        }
    }

    .week-time-table {
        background: #fafafa;
        border-radius: 4px;
        padding: 16px;

        .table-header {
            display: flex;
            align-items: center;
            padding: 12px 0;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            border-bottom: 1px solid #e8e8e8;

            .col-week {
                width: 100px;
            }

            .col-time {
                flex: 1;
            }
        }

        .table-body {
            .table-row {
                display: flex;
                align-items: center;
                padding: 12px 0;
                border-bottom: 1px solid #f0f0f0;

                &:last-child {
                    border-bottom: none;
                }

                .col-week {
                    width: 100px;
                    color: rgba(0, 0, 0, 0.65);
                }

                .col-time {
                    flex: 1;

                    :deep(.ant-picker) {
                        width: 180px;
                    }
                }

                .col-action {
                    margin-left: 12px;
                }
            }
        }
    }

    .tip-message {
        display: flex;
        align-items: center;
        margin-top: 20px;
        padding: 12px 16px;
        background: #fffbe6;
        border: 1px solid #ffe58f;
        border-radius: 4px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
    }
}
</style>
