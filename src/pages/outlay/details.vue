<!-- 菜品 -->
<template>
    <div class="outlay-details" v-if="!state.isDetailPackage">
        <div class="pageHead">
            <div class="pageHead_title">
                <ArrowLeftOutlined class="back" @click="handleBack" :style="{ color: store.sysColor }" />
                已定套餐详情
            </div>
        </div>
        <div class="pageBody">
            <ETable
                :columns="columns"
                :scroll="{ y: `calc(${clientHeight} - 249px)` }"
                :data-source="state.dataSource"
                :loading="state.loading"
                :paginations="state.pagination"
            >
                <template #bodyCell="{ column, text, record }">
                    <template v-if="column.dataIndex == 'isServing'">
                        <a-badge
                            :status="record.isServing == 1 ? 'success' : 'error'"
                            :text="record.isServing == 1 ? '已就餐' : '未就餐'"
                        />
                    </template>

                    <template v-else-if="column.dataIndex == 'operate'">
                        <a-button type="link" class="btn-link-color" @click="showPackageDetail(record)">套餐详情</a-button>
                        <a-button
                            type="link"
                            danger
                            class="btn-link-color"
                            v-if="!record.isDeadline"
                            @click="handleDelete(record.id)"
                        >
                            取消订餐
                        </a-button>
                    </template>
                    <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip>
                </template>
            </ETable>
        </div>
    </div>
    <DetailPackage v-else v-model:isDefinedDetails="state.isDetailPackage" :paramsEdit="state.currentPackage" />
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import Http from '@/utils/http.js'
import DetailPackage from './detailPackage.vue'
const clientHeight = ref(`${document.body.clientHeight}px`)
const store = useStore()
const props = defineProps({
    isDefinedDetails: {
        type: Boolean,
        default: false,
    },
    paramsEdit: {
        type: Object,
        default: () => {},
    },
})
const emit = defineEmits(['update:isDefinedDetails'])
const columns = [
    { title: '就餐日期', dataIndex: 'orderDate' },
    { title: '类型', dataIndex: 'mealSetTypeName' },
    { title: '套餐名称', dataIndex: 'mealSetVersionName' },
    { title: '就餐状态', dataIndex: 'isServing' },
    { title: '金额（元）', dataIndex: 'totalPrice' },
    { title: '操作', dataIndex: 'operate', width: 160 },
]

const state = reactive({
    currentPackage: {},
    loading: false,
    dataSource: [],
    isDetailPackage: false,
    pagination: {
        pageNo: 1,
        pageSize: 99999,
        total: 0,
    },
})
// 初始化数据
const getInitData = () => {
    state.loading = true
    Http.post('/cloud/canteenStudentOrder/canteenStudentOrderCount', props.paramsEdit)
        .then(({ data }) => {
            state.dataSource = data
        })
        .catch(err => {
            console.log('err: ', err)
        })
        .finally(() => {
            state.loading = false
        })
}

// 到套餐管理详情
const showPackageDetail = (item = {}) => {
    state.currentPackage = item
    state.currentPackage.userType = props.paramsEdit.userType
    state.isDetailPackage = true
}

// 删除设备
const handleDelete = id => {
    const { userType } = props.paramsEdit
    yConfirm('取消', '是否取消本次订餐？', '确 认', '取 消').then(res => {
        res &&
            Http.post('/cloud/canteenStudentOrder/delete', { ids: [id], userType }).then(({ message }) => {
                YMessage.success(message)
                getInitData()
            })
    })
}

// 返回
const handleBack = () => {
    emit('update:isDefinedDetails', false)
}
onMounted(() => {
    getInitData()
})
</script>

<style lang="less" scoped>
.outlay-details {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;

            .back {
                font-size: 16px;
            }
        }
    }

    .pageBody {
        padding: 20px;

        .create-btn {
            text-align: right;
            margin-bottom: 20px;
        }
    }
}
</style>
