<!-- 费用 -->
<template>
    <div class="outlay">
        <template v-if="!state.isDefinedDetails">
            <div class="pageHead">
                <div class="pageHead_title">
                    <a-tabs v-model:activeKey="state.activeKey" @change="handlerActiveKey">
                        <a-tab-pane v-for="item in mealSetTypes" :key="item.id" :tab="item.name"></a-tab-pane>
                    </a-tabs>
                </div>
            </div>
            <div class="pageBody">
                <div class="day-week-month-set">
                    <a-radio-group
                        class="date-query"
                        v-model:value="state.orderType"
                        @change="handlerOrderTypeTabs"
                        button-style="solid"
                    >
                        <a-radio-button value="day">日</a-radio-button>
                        <a-radio-button value="week">周</a-radio-button>
                        <a-radio-button value="month">月</a-radio-button>
                    </a-radio-group>
                    <a-button v-auth="'canteenMachine.outlay.receiptConfig'" @click="state.collectionConfigOpen = true">
                        收款配置
                    </a-button>
                </div>
                <searchForm
                    v-model:formState="state.form"
                    :formList="formList"
                    @submit="queryInitData"
                    @reset="queryInitData"
                    mb-20
                >
                    <template #rangePicker>
                        <a-date-picker
                            v-if="state.orderType == 'day'"
                            v-model:value="state.dayTime"
                            format="YYYY-MM-DD"
                            valueFormat="YYYY-MM-DD"
                        />

                        <a-date-picker
                            v-else-if="state.orderType == 'week'"
                            picker="week"
                            :allowClear="false"
                            v-model:value="state.weekTime"
                            placeholder="开始周 至 结束周"
                            :format="customWeekStartEndFormat"
                            @change="handleChangeRange"
                        />
                        <a-date-picker
                            v-else-if="state.orderType == 'month'"
                            v-model:value="state.monthTime"
                            format="YYYY-MM"
                            valueFormat="YYYY-MM"
                            picker="month"
                        />
                    </template>
                </searchForm>
                <div class="create-btn">
                    <a-button v-auth="'canteenMachine.outlay.export'" @click="handleExport">导出</a-button>
                </div>
                <ETable
                    :columns="state.activeKey ? columns1 : columns0"
                    :data-source="state.dataSource"
                    :paginations="state.pagination"
                    :loading="state.loading"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, text, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                        <template v-else-if="column.dataIndex == 'operate'">
                            <a-button
                                type="link"
                                class="btn-link-color"
                                v-auth="'canteenMachine.outlay.details'"
                                @click="handleOutlayDetails(record.userId)"
                            >
                                已定套餐详情
                            </a-button>
                        </template>

                        <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip>
                    </template>
                </ETable>
            </div>
            <CollectionConfigModal v-model:open="state.collectionConfigOpen" />
        </template>
        <Details v-else v-model:isDefinedDetails="state.isDefinedDetails" :paramsEdit="state.paramsEdit" />
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import Http from '@/utils/http.js'
import dayjs from 'dayjs'
import Details from './details.vue'
import CollectionConfigModal from './collectionConfigModal.vue'

const mealSetTypes = [
    {
        id: 0,
        name: '学生',
    },
    {
        id: 1,
        name: '老师',
    },
]
const state = reactive({
    collectionConfigOpen: false,
    isDefinedDetails: false,
    paramsEdit: {},
    loading: false,
    dataSource: [],
    dayTime: '',
    weekTime: null,
    monthTime: null,
    orderType: 'day',
    form: {
        userName: '',
        orgName: '',
        startTime: '',
        endTime: '',
    },
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    downloadTitle: '',
    activeKey: 0,
})
const orgNametitle = computed(() => (state.activeKey ? '部门' : '班级'))

const formList = ref([
    {
        type: 'input',
        value: 'userName',
        label: '姓名',
    },
    {
        type: 'input',
        value: 'orgName',
        label: orgNametitle,
    },
    {
        type: 'select',
        value: 'servingStatus',
        label: '就餐状态',
        list: [
            { label: '全部', value: null },
            { label: '未就餐', value: 0 },
            { label: '已就餐', value: 1 },
        ],
    },
    {
        type: 'slot',
        value: 'rangePicker',
        label: '就餐时间',
        attrs: {
            valueFormat: 'YYYY-MM-DD',
        },
        span: 5,
    },
])
const columns0 = ref([
    { title: '序号', dataIndex: 'index' },
    { title: '学号', dataIndex: 'userNo' },
    { title: '姓名', dataIndex: 'userName' },
    { title: '班级', dataIndex: 'orgName' },
    { title: '付费总额（元）', dataIndex: 'totalPrice' },
    { title: '操作', dataIndex: 'operate', width: 100 },
])

const columns1 = ref([
    { title: '序号', dataIndex: 'index' },
    { title: '姓名', dataIndex: 'userName' },
    { title: '部门', dataIndex: 'orgName' },
    { title: '付费总额（元）', dataIndex: 'totalPrice' },
    { title: '操作', dataIndex: 'operate', width: 100 },
])
const dayjsWweekday = params => {
    if (state.orderType == 'week') {
        if (state.weekTime) {
            // 获取当前周从周二开始的第一天
            params.startTime = dayjs(state.weekTime).weekday(1).format('YYYY-MM-DD')
            // 获取当前周的最后一天（以周二为起始，最后一天是周一）
            params.endTime = dayjs(state.weekTime).weekday(7).format('YYYY-MM-DD')
            state.weekTime = dayjs(state.weekTime).weekday(1)
        } else {
            params.startTime = dayjs().weekday(1).format('YYYY-MM-DD')
            params.endTime = dayjs().weekday(7).format('YYYY-MM-DD')
            state.weekTime = dayjs().weekday(1)
        }
        state.downloadTitle = `${params.startTime}至${params.endTime}`
    } else if (state.orderType == 'month') {
        let startTime = dayjs().startOf('month').format('YYYY-MM-DD')
        let endTime = dayjs().endOf('month').format('YYYY-MM-DD')
        if (state.monthTime) {
            startTime = dayjs(state.monthTime).startOf('month').format('YYYY-MM-DD')
            endTime = dayjs(state.monthTime).endOf('month').format('YYYY-MM-DD')
        }
        state.monthTime = state.monthTime || dayjs().startOf('month').format('YYYY-MM')
        params.startTime = startTime
        params.endTime = endTime
        state.downloadTitle = state.monthTime
    } else {
        const dayTime = state.dayTime || dayjs().format('YYYY-MM-DD')
        params.startTime = dayTime
        params.endTime = dayTime
        state.dayTime = dayTime
        state.downloadTitle = dayTime
    }
}

const getInitData = async () => {
    // 费用管理-按日|周|月统计分页数据
    try {
        state.loading = true
        const params = {
            ...state.form,
            ...state.pagination,
            userType: state.activeKey,
        }
        dayjsWweekday(params)
        await Http.post('/cloud/canteenStudentOrder/pageCanteenStudentOrder', params)
            .then(({ data }) => {
                const { list, pageNo, pageSize, total } = data
                state.dataSource = list
                state.pagination.pageNo = pageNo || 1
                state.pagination.pageSize = pageSize || 10
                state.pagination.total = total
            })
            .catch(error => {
                state.dataSource = []
                console.log('getList error: ', error)
            })
    } catch (error) {
        console.log('getList error: ', error)
    } finally {
        state.loading = false
    }
}

// 分页
const handleTableChange = ({ current, pageSize, total }, filters, { field, order }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    state.form[`${field}Sort`] = order
    getInitData()
}

// 日月周切换
const handlerOrderTypeTabs = type => {
    state.monthTime = null
    getInitData()
}
// 查询重置
const queryInitData = item => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    // 重置
    if (!item) {
        state.dayTime = null
        state.weekTime = null
        state.monthTime = null
    }
    getInitData()
}
// 学生老师切换
const handlerActiveKey = () => {
    state.form.userName = ''
    state.form.orgName = ''
    state.form.servingStatus = null
    state.dayTime = null
    state.orderType = 'day'
    queryInitData('')
}
// 添加导出功能
const handleExport = async () => {
    try {
        state.loading = true
        const params = {
            ...state.form,
            ...state.pagination,
            orderType: state.orderType,
            userType: state.activeKey,
        }
        dayjsWweekday(params)
        const title = `${state.activeKey ? '部门' : '班级'}-智慧点餐${state.downloadTitle}费用.xlsx`
        Http.download('/cloud/canteenStudentOrder/studentOrderCountExport', 'post', params, title)
    } catch (error) {
        YMessage.error('导出失败：' + error.message)
    } finally {
        state.loading = false
    }
}
const customWeekStartEndFormat = (value, format) => {
    const data = `${dayjs(value)
        .weekday(1)
        .format(format || 'YYYY-MM-DD')} 至 ${dayjs(value)
        .weekday(7)
        .format(format || 'YYYY-MM-DD')}`
    return data
}
// 一周时间
const handleChangeRange = event => {
    const dataTime = customWeekStartEndFormat(event, 'YYYY-MM-DD')
    const [startTime, endTime] = dataTime.split(' 至 ')
    return { startTime, endTime }
}

// 已定套餐详情
const handleOutlayDetails = userId => {
    state.paramsEdit = {
        userId,
        startTime: '',
        endTime: '',
        userType: state.activeKey,
    }
    dayjsWweekday(state.paramsEdit)
    state.isDefinedDetails = true
}

onMounted(() => {
    getInitData()
})
</script>

<style lang="less" scoped>
.outlay {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            text-align: left;
            font-style: normal;
            :deep(.ant-tabs-nav) {
                margin: 0;
                .ant-tabs-nav-wrap {
                    height: 62px;
                }
            }
        }
    }

    .pageBody {
        padding: 20px;

        .day-week-month-set {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .date-query {
            margin-bottom: 20px;
        }

        .create-btn {
            text-align: right;
            margin-bottom: 20px;
        }
    }
}
</style>
