<template>
    <YModal v-model:open="props.open" title="收款配置" :bodyStyle="{ padding: '24px' }" @cancel="handleCancel">
        <a-form
            :model="state.collectionConfigForm"
            ref="collectionConfigRef"
            layout="vertical"
            class="collection-config"
            :rules="collectionConfigRule"
        >
            <a-form-item label="收款方式：" name="payeeWay">
                <a-radio-group v-model:value="state.collectionConfigForm.payeeWay" name="radioGroup">
                    <a-radio :value="1">其他</a-radio>
                    <a-radio :value="2">校易付</a-radio>
                </a-radio-group>
            </a-form-item>
            <div v-show="state.collectionConfigForm.payeeWay == 1">
                <a-form-item label="收款状态：" name="status">
                    <a-radio-group v-model:value="state.collectionConfigForm.status" button-style="solid">
                        <a-radio :value="true">开启</a-radio>
                        <a-radio :value="false">关闭</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="收款二维码：" name="qrcode">
                    <UploadImage
                        @emitFileList="handlerEmitFileList"
                        :feedbackFileList="state.feedbackFileList"
                        :sizeNum="2"
                        accept="image/*"
                        tips="最多上传1张，建议上传图片为大小不超过2M"
                    />
                </a-form-item>

                <a-form-item label="说明：" name="description">
                    <a-textarea
                        v-model:value="state.collectionConfigForm.description"
                        show-count
                        :maxlength="100"
                        placeholder="请输入"
                    />
                </a-form-item>
            </div>
            <div v-show="state.collectionConfigForm.payeeWay == 2">
                <a-form-item label="收款类型：" name="payeeType">
                    <a-select
                        v-model:value="state.collectionConfigForm.payeeType"
                        size="middle"
                        style="width: 100%"
                        :options="payeeWayOptions"
                        placeholder="请选择"
                    ></a-select>
                </a-form-item>
                <a-form-item label="收款商户：" name="merchantId">
                    <a-select
                        v-model:value="state.collectionConfigForm.merchantId"
                        size="middle"
                        style="width: 100%"
                        :options="merchantIdOptions"
                        :fieldNames="{ label: 'merchantName', value: 'merchantId' }"
                        placeholder="请选择"
                    ></a-select>
                </a-form-item>
                <a-form-item label="结算方式：" name="settlementWay">
                    <a-select
                        v-model:value="state.collectionConfigForm.settlementWay"
                        size="middle"
                        style="width: 100%"
                        :options="settlementWayOptions"
                        placeholder="请选择"
                    ></a-select>
                </a-form-item>
                <p class="tips">说明：结算方式为月结时，移动端每个月的统计页底部出现‘立即缴费’按钮供用户进行缴费</p>
            </div>
        </a-form>
        <template #footer>
            <a-button key="back" @click="handleCancel">取消</a-button>
            <a-button key="submit" type="primary" :loading="state.loading" @click="handlCollectionConfigOk">确定</a-button>
        </template>
    </YModal>
</template>

<script setup>
import { reactive, watch } from 'vue'
import Http from '@/utils/http.js'

const collectionConfigRule = {
    payeeType: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    status: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    qrcode: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    description: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    payeeWay: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    merchantId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    settlementWay: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
}
const payeeWayOptions = [
    { label: '微信支付', value: 1 },
    // { label: '支付宝', value: '2' },
]
const settlementWayOptions = [{ label: '月结', value: 1 }]
const collectionConfigRef = shallowRef()
const merchantIdOptions = shallowRef([])

const props = defineProps({
    open: {
        type: Boolean,
        default: false,
    },
})
const state = reactive({
    loading: false,
    feedbackFileList: [],
    collectionConfigForm: {
        orderType: 'other',
        status: '0',
        qrcode: '',
        description: '',
        payeeWay: 1,
        merchantId: null,
        settlementWay: null,
    },
})

const emit = defineEmits(['update:open', 'emitGetInitData'])
// 更新图片
const handlerEmitFileList = list => {
    state.collectionConfigForm.qrcode = list[0]?.url || ''
    collectionConfigRef.value.validateFields('qrcode')
}

// 关闭收款配置
const handleCancel = () => {
    collectionConfigRef.value.resetFields()
    emit('update:open', false)
}
// 提交收款配置
const handlCollectionConfigOk = () => {
    let _rules = ['payeeWay', 'status', 'qrcode', 'description']
    if (state.collectionConfigForm.payeeWay == 2) {
        _rules = ['payeeType', 'merchantId', 'settlementWay']
    }
    collectionConfigRef.value
        .validate(_rules)
        .then(() => {
            state.loading = true
            Http.post('/cloud/canteenPaymentSetting/create', state.collectionConfigForm)
                .then(({ message }) => {
                    YMessage.success(message)
                    handleCancel()
                    emit('emitGetInitData')
                })
                .finally(() => (state.loading = false))
        })
        .catch(err => {
            console.log('err:', err)
        })
}
// 收款配置
const handlerCollectionConfigOpen = async () => {
    state.collectionConfigOpen = true
    await Http.get('/cloud/canteenPaymentSetting/findMerchantList').then(({ data }) => {
        merchantIdOptions.value = data
    })
    await Http.get('/cloud/canteenPaymentSetting/get').then(({ data }) => {
        state.collectionConfigForm = data
        state.feedbackFileList = []
        if (data.qrcode) {
            state.feedbackFileList = [
                {
                    url: data.qrcode,
                },
            ]
        }
    })
}
watch(
    () => props.open,
    v => {
        v && handlerCollectionConfigOpen()
    },
)
</script>

<style lang="less" scoped>
.tips {
    color: @warning-color;
    margin-top: 10px;
}

.collection-config {
    :deep(.ant-form-item) {
        margin-bottom: 10px;
    }
}
</style>
