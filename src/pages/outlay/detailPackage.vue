<template>
    <div class="detail-package">
        <div class="pageHead">
            <div class="pageHead_title">
                <ArrowLeftOutlined class="back" @click="handleBack" :style="{ color: store.sysColor }" />
                套餐详情
            </div>
        </div>
        <div class="pageBody">
            <a-form :model="state.formState" ref="formRef" layout="vertical">
                <a-form-item label="套餐名称：" name="mealSetVersionName">
                    <a-input
                        v-model:value.trim="state.formState.mealSetVersionName"
                        show-count
                        :maxlength="20"
                        placeholder="请输入"
                        :disabled="isDisabled"
                    />
                </a-form-item>

                <a-form-item label="套餐类型：" name="mealSetTypeName">
                    <a-input
                        v-model:value.trim="state.formState.mealSetTypeName"
                        show-count
                        :maxlength="20"
                        placeholder="请输入"
                        :disabled="isDisabled"
                    />
                </a-form-item>

                <a-form-item label="已选菜品：" name="dishList">
                    <div class="selected-dishes">
                        <template v-for="it in state.formState.dishList">
                            <div class="selected-dishes-item" v-if="!it.isSingleDish" :key="it.dishId">
                                <a-avatar shape="square" :size="86" :src="it.dishLogo"></a-avatar>
                                <div class="selected-dishes-item-name">{{ it.dishName }}</div>
                                <div class="selected-dishes-item-price">￥{{ it.dishPrice }}</div>
                            </div>
                        </template>
                    </div>
                </a-form-item>
                <a-form-item label="自选额外菜品：" name="l">
                    <div class="selected-dishes">
                        <template v-for="it in state.formState.singleDishList">
                            <div class="selected-dishes-item" v-if="it.isSingleDish" :key="it.dishId">
                                <a-avatar shape="square" :size="86" :src="it.dishLogo"></a-avatar>
                                <div class="selected-dishes-item-name">{{ it.dishName }}</div>
                                <div class="selected-dishes-item-price">￥{{ it.dishPrice }}</div>
                            </div>
                        </template>
                    </div>
                </a-form-item>
                <a-form-item label="套餐价格：" name="totalPrice">
                    <a-input-number
                        style="width: 100%"
                        :step="0.01"
                        string-mode
                        v-model:value.trim="state.formState.totalPrice"
                        placeholder="请输入价格（保留两位小数点，如9.99）"
                        addon-after="元"
                        :disabled="isDisabled"
                    ></a-input-number>
                </a-form-item>
                <a-form-item label="就餐状态：" name="isServing">
                    <p class="meal-status">{{ state.formState.isServing ? ' 已就餐' : ' 未就餐' }}</p>
                </a-form-item>
            </a-form>
        </div>
    </div>
</template>
<script setup>
import { reactive, onMounted } from 'vue'
import Http from '@/utils/http.js'
const store = useStore()

const props = defineProps({
    isDefinedDetails: {
        type: Boolean,
        default: false,
    },
    isDisabled: {
        type: Boolean,
        default: true,
    },
    paramsEdit: {
        type: Object,
        default: () => ({}),
    },
})
const emit = defineEmits(['update:isDefinedDetails'])

const state = reactive({
    formState: {
        mealSetVersionName: null,
        mealSetTypeName: null,
        totalPrice: null,
        dishList: [],
        singleDishList: [],
        isServing: null,
    },
})
// 单个套餐详情
const initPage = () => {
    const { userType, id } = props.paramsEdit
    Http.get('/cloud/canteenStudentOrder/get', { id, userType }).then(({ data }) => {
        state.formState = data
    })
}

// 返回
const handleBack = () => {
    emit('update:isDefinedDetails', false)
}
onMounted(() => {
    initPage()
})
</script>

<style lang="less" scoped>
.detail-package {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;

            .back {
                font-size: 16px;
            }
        }
    }

    .pageBody {
        padding: 20px;
        width: 68%;
        margin: 0 auto;

        :deep(.ant-form-item) {
            margin-bottom: 20px;
        }

        .selected-dishes {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;

            .selected-dishes-item {
                width: 100px;

                .selected-dishes-item-name {
                    // 超出1行省略号
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .selected-dishes-item-price {
                    color: #999999ff;
                }
            }
        }

        .meal-status {
            color: @primary-color;
        }
    }
}
</style>
