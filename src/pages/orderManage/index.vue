<template>
    <div class="order-manage">
        <div class="pageHead">
            <div class="pageHead_title">订单管理</div>
        </div>
        <div class="pageBody">
            <a-radio-group v-model:value="state.orderType" button-style="solid">
                <a-radio-button value="tradingOrder" v-auth="tradeOrder">订餐交易订单</a-radio-button>
                <a-radio-button value="refundOrder" v-auth="refundOrder">订餐退费订单</a-radio-button>
            </a-radio-group>
            <component :is='state.components'></component>
            <Empty v-if="state.isEmpty" tips="暂无权限" />
        </div>
    </div>
</template>

<script setup>
import { onMounted, reactive, watch } from 'vue'
const store = useStore()
const tradeOrder = 'canteenMachine.device.orderManag.tradeOrder'
const refundOrder = 'canteenMachine.device.orderManag.refundOrder'
const tradingOrderTemplate = defineAsyncComponent(() => import('./tradingOrder.vue'))
const refundOrderTemplate = defineAsyncComponent(() => import('./refundOrder.vue'))

const state = reactive({
    isEmpty: false,
    orderType: '',
    components: null
})

watch(() => state.orderType, (val) => {
    state.components = val == 'tradingOrder' ? tradingOrderTemplate : refundOrderTemplate
})
onMounted(() => {
    //按钮权限控制判断。 如果订餐交易订单存在， 则赋值，并不再向下执行
    const _tradeOrder = store.Perms.some(v => v == tradeOrder)
    if (_tradeOrder) {
        state.isEmpty = false
        state.orderType = 'tradingOrder'
        state.components = tradingOrderTemplate
    } else {
        // 如果订餐退费订单存在， 则赋值，并不再向下执行
        const _refundOrder = store.Perms.some(v => v == refundOrder)
        if (_refundOrder) {
            state.isEmpty = false
            state.orderType = 'refundOrder'
            state.components = refundOrderTemplate
        } else {
            state.isEmpty = true
        }
    }
})
</script>

<style lang="less" scoped>
.order-manage {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;
        }
    }

    .pageBody {
        padding: 20px;

    }
}
</style>
