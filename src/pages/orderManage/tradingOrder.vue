<template>
    <div class="trading-order">
        <a-space class="reset-space" :size="12">
            <a-card class="card-item" hoverable v-for="(item, index) in cardMetas" :key="index">
                <a-card-meta :title="`${state.cardMetasData[item.value]} ${item.unit}`">
                    <template #description>{{ item.description }}</template>
                </a-card-meta>
            </a-card>
        </a-space>

        <searchForm
            class="reset-search-form"
            v-model:formState="state.form"
            :formList="formList"
            @submit="queryInitData"
            @reset="queryInitData"
        ></searchForm>

        <ETable
            :columns="columns"
            :minH="450"
            :data-source="state.dataSource"
            :paginations="state.pagination"
            :scroll="{ x: 1500 }"
            :loading="state.loading"
            @change="handleTableChange"
        >
            <template #bodyCell="{ column, text, record, index }">
                <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                <template v-else-if="column.dataIndex == 'payMethod'">{{ text == 1 ? '微信' : '支付' }}</template>

                <template v-else-if="column.dataIndex == 'operate'">
                    <template v-if="[2, 5].includes(record.orderStatus)">
                        <a-button
                            type="link"
                            class="btn-link-color"
                            @click="handlerRefundOpen(record)"
                            v-auth="'canteenMachine.device.orderManag.refundMoney'"
                        >
                            退款
                        </a-button>
                    </template>
                </template>
                <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip>
            </template>
        </ETable>

        <YModal
            v-model:open="state.refundOpen"
            title="退款申请"
            width="700px"
            :bodyStyle="{ padding: '24px' }"
            @cancel="handleRefundCancel"
        >
            <ul class="refund-list">
                <li class="refund-item" v-for="item in refundApplication" :key="item.key">
                    <span class="refund-item-title">{{ item.title }}</span>
                    <span class="refund-item-money">{{ state.refundForm[item.key] }}</span>
                </li>
            </ul>

            <a-form class="refund-form" :model="state.refundForm" ref="formRef" layout="vertical">
                <a-form-item name="refundReason">
                    <template #label>
                        <div class="refund-title">
                            <a-divider type="vertical" class="refund-divider" />
                            退款原因：
                        </div>
                    </template>
                    <a-textarea
                        v-model:value.trim="state.refundForm.refundReason"
                        placeholder="请输入退款原因"
                        show-count
                        :maxlength="100"
                        :auto-size="{ minRows: 6, maxRows: 5 }"
                    />
                </a-form-item>
            </a-form>
            <template #footer>
                <a-button key="back" @click="handleRefundCancel">取消</a-button>
                <a-button key="submit" type="primary" :loading="state.refundLoading" @click="handleRefundOk">确定</a-button>
            </template>
        </YModal>
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import Http from '@/utils/http.js'

// 今日订单
const cardMetas = [
    {
        unit: '笔',
        description: '今日订单',
        value: 'todayOrderNum',
    },
    {
        unit: '元',
        description: '今日缴费总额',
        value: 'todayPayAmount',
    },
    {
        unit: '笔',
        description: '订单总数',
        value: 'totalOrderNum',
    },
    {
        unit: '元',
        description: '订单总金额',
        value: 'totalPayAmount',
    },
]
const formList = shallowRef([
    {
        type: 'input',
        value: 'orderNo',
        label: '内部订单号',
    },
    {
        type: 'input',
        value: 'transactionId',
        label: '交易流水号',
    },
    {
        type: 'input',
        value: 'orderUserName',
        label: '姓名',
    },

    {
        type: 'input',
        value: 'payPhone',
        label: '手机号',
    },
    {
        type: 'select',
        value: 'orderStatus',
        label: '订单状态',
        list: [
            { label: '全部', value: null },
            { label: '未支付', value: 1 },
            { label: '已支付', value: 2 },
            { label: '关闭', value: 3 },
        ],
    },
    {
        type: 'select',
        value: 'payMethod',
        label: '支付渠道',
        list: [
            { label: '全部', value: null },
            { label: '微信', value: 1 },
            { label: '支付宝', value: 2 },
        ],
    },
    {
        type: 'rangePicker',
        label: '创建订单时间',
        value: ['crateTime', 'endTime'],
        voidValue: 'crateEndTime',
        span: 7,
        attrs: {
            valueFormat: 'YYYY-MM-DD',
            format: 'YYYY-MM-DD',
            showTime: true,
        },
    },
    {
        type: 'rangePicker',
        label: '支付时间',
        value: ['startPayTime', 'endPayTime'],
        voidValue: 'startPayendPayTime',
        span: 7,
        attrs: {
            valueFormat: 'YYYY-MM-DD',
            format: 'YYYY-MM-DD',
            showTime: true,
        },
    },
])

const columns = [
    { title: '序号', dataIndex: 'index', key: 'index', width: 80 },
    { title: '内部订单号', dataIndex: 'orderNo' },
    { title: '外部订单号', dataIndex: 'tradeNo' },
    { title: '交易流水号', dataIndex: 'transactionId' },
    { title: '姓名', dataIndex: 'orderUserName' },
    { title: '手机号', dataIndex: 'payPhone' },
    { title: '金额', dataIndex: 'payAmount' },
    { title: '订单状态', dataIndex: 'orderStatusName' },
    { title: '创建订单时间', dataIndex: 'createTime', sorter: true },
    { title: '支付渠道', dataIndex: 'payMethod' },
    { title: '支付设备', dataIndex: 'paySource' },
    { title: '支付时间', dataIndex: 'payTime', sorter: true },
    { title: '操作', dataIndex: 'operate', fixed: 'right', width: 80 },
]

const refundApplication = [
    { title: '订餐项目：', key: 'title' },
    { title: '缴费人：', key: 'orderUserName' },
    { title: '手机号：', key: 'payPhone' },
    { title: '缴费金额：', key: 'payAmount' },
]
const state = reactive({
    loading: false,
    form: {
        orderNo: '',
        transactionId: '',
        orderUserName: '',
        payPhone: '',
        orderStatus: null,
        payMethod: null,
        crateTime: '',
        endTime: '',
        startPayTime: '',
        endPayTime: '',
    },
    dataSource: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    // 退款申请
    refundForm: {
        payId: '',
        title: '',
        orderUserName: '',
        payPhone: '',
        payAmount: '',
        refundReason: '',
    },
    refundOpen: false,
    refundLoading: false,
    cardMetasData: {
        todayOrderNum: 0,
        todayPayAmount: 0,
        totalOrderNum: 0,
        totalPayAmount: 0,
    },
    sort: {
        field: '',
        order: '',
    },
})
// 退款申请弹框
const handlerRefundOpen = item => {
    state.refundForm = item
    state.refundForm.payId = item.id
    state.refundForm.refundReason = ''
    state.refundOpen = true
}
// 常规支付-交易订单-分页
const getInitData = () => {
    state.loading = true
    const params = {
        ...state.form,
        ...state.pagination,
        businessType: 3, // 3.智慧点餐
    }
    if (state.sort.field) {
        params.field = state.sort.field
        params.order = state.sort.order
    }
    Http.post('/campuspay/general-mgmt/pay-order/paymentOrderPage', params)
        .then(({ data }) => {
            const { pageNo, pageSize, total, list } = data
            state.dataSource = list
            state.pagination.pageNo = pageNo
            state.pagination.pageSize = pageSize
            state.pagination.total = total
        })
        .finally(() => {
            state.loading = false
        })
}
// 分页
const handleTableChange = ({ current, pageSize, total }, filters, { field, order }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    state.sort.field = ''
    state.sort.order = ''
    if (order) {
        state.sort.field = field == 'createTime' ? 'create_time' : 'pay_time'
        state.sort.order = order == 'ascend' ? 'asc' : 'desc'
    }
    getInitData()
}
// 查询 重置
const queryInitData = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getInitData()
}
// 常规支付-交易订单-统计
const getOrderStatistics = () => {
    // 3.智慧点餐 4.招生迎新 ，null：查询所有
    Http.post('/campuspay/general-mgmt/pay-order/orderStatistics', { businessType: 3 }).then(({ data }) => {
        state.cardMetasData = data
    })
}

// 退款申请取消
const handleRefundCancel = () => {
    state.refundOpen = false
    state.refundForm.refundReason = ''
}
// 退款申请确定
const handleRefundOk = () => {
    const params = {
        ...state.refundForm,
    }
    state.refundLoading = true
    Http.post('/campuspay/general-mgmt/pay-order/refund-request', params)
        .then(({ message }) => {
            YMessage.success(message)
            handleRefundCancel()
            getInitData()
        })
        .finally(() => {
            state.refundLoading = false
        })
}
onMounted(() => {
    getInitData()
    getOrderStatistics()
})
</script>

<style lang="less" scoped>
.trading-order {
    .reset-space {
        width: 100%;
        margin-top: 24px;

        .card-item {
            min-width: 140px;
            text-align: center;
        }
    }

    .reset-search-form {
        margin: 24px 0;
    }

    :deep(.ant-table-column-has-sorters) {
        .ant-table-column-sorters {
            justify-content: flex-start;

            .ant-table-column-title {
                flex: none;
            }
        }
    }
}

.refund-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    // 每行3列
    .refund-item {
        width: 50%;
        margin-bottom: 24px;

        .refund-item-title {
            font-size: 14px;
            font-weight: 400;
            color: #000000a6;
        }

        .refund-item-money {
            // 字数过多省略
            width: max-content;
            display: inline-block;
            width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: bottom;
        }
    }
}

.refund-title {
    color: #000000a6;

    .refund-divider {
        height: 16px;
        width: 3px;
        margin-left: 0;
        background-color: @primary-color;
    }
}
</style>
