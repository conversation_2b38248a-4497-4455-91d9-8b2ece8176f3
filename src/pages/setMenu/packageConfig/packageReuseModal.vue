<template>
    <YModal v-model:open="props.open" :width="460" :title="'套餐复用'" :bodyStyle="{ padding: '24px' }" @cancel="handleCancel">
        <a-form :model="state.packageReuseForm" ref="packageReuseRef" layout="vertical">
            <a-form-item class="modal-rest-form" label="当前套餐复用至：" name="weekStartEndDate">
                <a-select
                    v-model:value="state.copyWeekly"
                    mode="tags"
                    style="width: 100%"
                    placeholder="请选择时间周期"
                    :options="copyWeeklyOptions"
                ></a-select>
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button key="back" @click="handleCancel">取消</a-button>
            <a-button key="submit" :loading="state.loading" type="primary" @click="handlePackageReuse">确定</a-button>
        </template>
    </YModal>
</template>

<script setup>
import { reactive } from 'vue'
import Http from '@/utils/http.js'
import dayjs from 'dayjs'

const props = defineProps({
    open: {
        type: Boolean,
        default: false,
    },
    currentWeekly: {
        type: Object,
        default: () => {
            return { startDate: '', endDate: '' }
        },
    },
})
const copyWeeklyOptions = shallowRef([])
const emit = defineEmits(['update:open', 'emitInitPage'])
const state = reactive({
    copyWeekly: [],
    loading: false,
})

// 套餐复用关闭
const handleCancel = () => {
    state.copyWeekly = []
    emit('update:open', false)
}

// 套餐复用
const handlePackageReuse = () => {
    if (state.copyWeekly.length === 0) return YMessage.warning('请选择时间周期')
    const copyWeekly = state.copyWeekly.map(item => {
        const startDate = dayjs(item).weekday(1).format('YYYY-MM-DD')
        const endDate = dayjs(item).weekday(7).format('YYYY-MM-DD')
        return { startDate, endDate }
    })
    const params = {
        currentWeekly: props.currentWeekly,
        copyWeekly,
    }
    state.loading = true
    Http.post('/cloud/canteenMealSet/copy', params)
        .then(({ message }) => {
            YMessage.success(message)
            handleCancel()
            emit('emitInitPage')
        })
        .finally(() => {
            state.loading = false
        })
}

// 定义一个函数来获取一月的每个周次
function getWeeksInJanuary() {
    const weeks = []
    const startOfMonth = dayjs().startOf('month')
    const endOfMonth = dayjs().endOf('month')

    let currentDate = startOfMonth
    while (currentDate <= endOfMonth) {
        const weekStart = currentDate.startOf('week')
        const weekEnd = currentDate.endOf('week')

        // 确保周次在当月范围内
        // if (weekStart.isSameOrAfter(startOfMonth) && weekEnd.isSameOrBefore(endOfMonth)) {
        weeks.push({
            weekStart: weekStart.format('YYYY-MM-DD'),
            weekEnd: weekEnd.format('YYYY-MM-DD'),
        })
        // }

        currentDate = weekEnd.add(1, 'day')
    }
    return weeks
}

// 获取当前月和下个月的周数
function getWeeksWithDisabledStatus() {
    const currentWeek = dayjs().week()
    const currentYear = dayjs().year()
    const startOfCurrentMonth = dayjs().startOf('month')
    const endOfNextMonth = dayjs().add(1, 'month').endOf('month')
    const weeks = []
    let currentDate = startOfCurrentMonth
    while (currentDate <= endOfNextMonth) {
        const weekStart = currentDate.weekday(1)
        const weekEnd = currentDate.weekday(7)
        const weekNum = weekStart.week()
        const year = weekStart.year()

        // 判断当前周是否小于本周
        const isDisabled = year === currentYear && weekNum <= currentWeek
        if (!isDisabled) {
            weeks.push({
                label: weekStart.format('YYYY-MM-DD') + ' 至 ' + weekEnd.format('YYYY-MM-DD'),
                value: weekStart.format('YYYY-MM-DD'),
            })
        }

        currentDate = weekEnd.add(1, 'day')
    }

    return weeks
}

watch(
    () => props.open,
    newVal => {
        if (newVal) {
            copyWeeklyOptions.value = getWeeksWithDisabledStatus()
        }
    },
)
</script>

<style lang="less" scoped></style>
