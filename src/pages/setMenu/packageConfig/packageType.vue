<template>
    <div class="detail-package">
        <div class="pageHead">
            <div class="pageHead_title">
                <ArrowLeftOutlined class="back" @click="handleBack" :style="{ color: store.sysColor }" />
                套餐类型设置
            </div>
        </div>
        <div class="pageBody">
            <a-form class="rest-form" :model="state.formState" ref="packageTypeRef" layout="inline">
                <div class="form-item" v-for="(it, idx) in state.formState.mealSetTypes" :key="idx">
                    <a-form-item label="套餐类型：" :name="['mealSetTypes', idx, 'code']"
                        :rules="{ required: true, message: '请选择', trigger: ['change', 'blur'] }">
                        <a-input :value="it.name" style="width: 240px" />
                    </a-form-item>
                    <a-form-item label="类型展示时间：" :name="['mealSetTypes', idx, 'startEndTime']"
                        :rules="{ required: it.switchStatus, message: '请选择', trigger: ['change', 'blur'] }">
                        <a-time-range-picker v-model:value="it.startEndTime" @change="changeStartEndTime($event, it)"
                            format="HH:ss" valueFormat="HH:ss" :allowClear="it.switchStatus" />
                    </a-form-item>
                    <a-form-item label="">
                        <a-switch v-model:checked="it.switchStatus" @change="changeSwitchStatus($event, it, idx)" />
                    </a-form-item>
                </div>
            </a-form>
        </div>

        <div class="footer">
            <a-button @click="handleBack">取消</a-button>
            <a-button type="primary" :loading="state.loading" @click="handerSubmit">确定</a-button>
        </div>
    </div>
</template>
<script setup>
import { reactive, onMounted } from 'vue'
import Http from '@/utils/http.js'
const store = useStore()
const emit = defineEmits(['update:isDishType', 'emitInitData'])

const packageTypeRef = shallowRef()
const props = defineProps({
    isDishType: {
        type: Boolean,
        default: false,
    },
})
const state = reactive({
    loading: false,
    formState: {
        mealSetTypes: [
            {
                code: null,
                startEndTime: [],
                switchStatus: true,
            },
        ],
    },
})
// 套餐管理-获取餐次类型设置
const initPage = () => {
    Http.get('/cloud/canteenMealSetType/get').then(({ data }) => {
        if (data.length) {
            state.formState.mealSetTypes = data.map(v => {
                return {
                    ...v,
                    startEndTime:v.endTime? [v.startTime, v.endTime]:null,
                }
            })
        }
    })
}

const changeSwitchStatus = (checked, item, idx) => {
    if (!checked) {
        // 清除该字段的校验状态
        if (packageTypeRef.value) {
            packageTypeRef.value.clearValidate([['mealSetTypes', idx, 'startEndTime']])
        }
    }
}
// 返回
const handleBack = () => {
    emit('update:isDishType', false)
}

// 确定
const handerSubmit = () => {
    packageTypeRef.value.validate().then(res => {
        if (res) {
            Http.post('/cloud/canteenMealSetType/update', state.formState).then(({ message }) => {
                YMessage.success(message)
                handleBack()
                emit('emitInitData')
            })
        }
    })
}
// 时间选择
const changeStartEndTime = (event, item) => {
    const [startTime = '', endTime = ''] = event || []
    item.startTime = startTime
    item.endTime = endTime
}

onMounted(() => {
    initPage()
})
</script>

<style lang="less" scoped>
.detail-package {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;

            .back {
                font-size: 16px;
            }
        }
    }

    .pageBody {
        padding: 20px;
        height: calc(100vh - 160px);
        overflow-y: auto;

        .rest-form {
            width: 50%;
            margin: 0 auto;

            .form-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 16px;
            }
        }

        :deep(.ant-form-item) {
            margin-bottom: 20px;
        }

        .selected-dishes {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;

            .selected-dishes-item {
                width: 100px;

                .selected-dishes-item-name {
                    // 超出1行省略号
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .selected-dishes-item-price {
                    color: #999999ff;
                }
            }
        }

        .meal-status {
            color: @primary-color;
        }
    }

    .footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        z-index: 999;
        border-top: 1px solid @border-color-base;
        background-color: @body-background;
        padding: 20px 0;
    }
}
</style>
