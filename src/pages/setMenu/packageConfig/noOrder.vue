<template>
    <div class="detail-package" v-if="!state.showLog">
        <div class="pageHead">
            <div class="pageHead_title">
                <ArrowLeftOutlined class="back" @click="handleBack" :style="{ color: store.sysColor }" />
                暂不点餐
            </div>
        </div>
        <div class="pageBody">
            <searchForm
                v-model:formState="state.form"
                :formList="formList"
                @submit="queryInitData"
                @reset="queryInitData"
            ></searchForm>
            <div class="create-btn">
                <a-button type="primary" @click="openNo">添加</a-button>
                <a-button @click="openLog">操作日志</a-button>
            </div>
            <a-spin :spinning="state.loading">
                <div class="card_list">
                    <template v-if="state.cardList.length">
                        <div class="card-item" v-for="item in state.cardList" :key="item.id">
                            <div class="card-item__header">
                                <div class="card-title" :title="item.name">{{ item.name }}</div>
                                <a-popover trigger="hover" placement="bottomRight" overlay-class-name="card-action-popover">
                                    <template #content>
                                        <div class="card-actions">
                                            <div
                                                v-for="action in getActionList(item)"
                                                :key="action.key"
                                                class="card-actions__item"
                                                @click="handleCardAction(action.key, item)"
                                            >
                                                {{ action.label }}
                                            </div>
                                        </div>
                                    </template>
                                    <MoreOutlined class="card-more" />
                                </a-popover>
                            </div>
                            <div class="card-item__row">
                                <span class="card-label">不点餐类型：</span>
                                <span class="card-value">{{ item.mealSetTypeName }}</span>
                            </div>
                            <div class="card-item__row">
                                <span class="card-label">不点餐时间：</span>
                                <span class="card-value">{{ item.startDate }} - {{ item.endDate }}</span>
                            </div>
                            <div class="card-item__row card-item__row--people">
                                <span class="card-label">人员信息：</span>

                                <div class="card-value card-people" @click="handleShowPeopleInfo(item)">
                                    <span class="card-person">
                                        {{ item.personnelNames || '-' }}
                                    </span>
                                </div>
                            </div>
                            <div class="card-item__row">
                                <span class="card-label">规则状态：</span>
                                <span
                                    class="card-value card-status"
                                    :class="item.status ? 'card-status--enabled' : 'card-status--disabled'"
                                >
                                    {{ item.status ? '已启用' : '已禁用' }}
                                </span>
                            </div>
                        </div>
                    </template>
                    <Empty v-else class="card-empty" tips="暂无暂不点餐规则" :emptyStyle="{ width: '100%', padding: '80px 0' }" />
                </div>
                <div class="card-pagination" v-if="state.pagination.total">
                    <a-pagination
                        :current="state.pagination.pageNo"
                        :page-size="state.pagination.pageSize"
                        :total="state.pagination.total"
                        :page-size-options="['10', '20', '30', '50']"
                        show-size-changer
                        hide-on-single-page
                        :show-total="total => `共 ${total} 条记录`"
                        @change="handlePageChange"
                        @showSizeChange="handlePageSizeChange"
                    />
                </div>
            </a-spin>
        </div>
    </div>
    <NoOrderLog v-else @back="handleLogBack" />
    <NoOrderEdit ref="noOrderEditRef" @submitSuccess="queryInitData"></NoOrderEdit>
    <PeopleInfoModal ref="peopleInfoModalRef"></PeopleInfoModal>
</template>
<script setup>
import Http from '@/utils/http.js'
import { reactive, onMounted, ref } from 'vue'
import NoOrderEdit from './noOrderEdit.vue'
import NoOrderLog from './noOrderLog.vue'
import PeopleInfoModal from './peopleInfoModal.vue'

const store = useStore()
const emit = defineEmits(['update:isDishType'])
const noOrderEditRef = ref(null)
const peopleInfoModalRef = ref(null)
/**
 * 查询条件配置
 */
const formList = ref([
    {
        type: 'input',
        value: 'name',
        label: '规则名称',
    },
    {
        type: 'input',
        value: 'peopleName',
        label: '人员名称',
    },
    {
        type: 'select',
        value: 'status',
        label: '规则状态',
        list: [
            { label: '全部', value: null },
            { label: '启用', value: 1 },
            { label: '禁用', value: 0 },
        ],
    },
])

/**
 * 打开暂不点餐配置抽屉
 */
const openNo = () => {
    noOrderEditRef.value.showeModal()
}

const editorProps = item => {
    Http.get('/cloud/canteenNoOrderSetting/get', {
        id: item.id,
    }).then(res => {
        const data = {
            id: res.data.id,
            mealSetTypeIds: res.data.mealSetTypeIds,
            name: res.data.name,
            personnelName: res.data.personnelNames,
            range: [res.data.startDate, res.data.endDate],
            personnelList: res.data.personnelList,
        }
        noOrderEditRef.value.showeModal(data)
    })
}

/**
 * 进入操作日志视图
 */
const openLog = () => {
    state.showLog = true
}

/**
 * 从操作日志返回暂不点餐列表
 */
const handleLogBack = () => {
    state.showLog = false
}
defineProps({
    isDishType: {
        type: Boolean,
        default: false,
    },
})
/**
 * 页面内部状态
 */
const state = reactive({
    loading: false,
    showLog: false,
    form: {
        ruleName: null,
        peopleName: null,
        servingStatus: null,
    },
    cardList: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
})

/**
 * 卡片操作类型常量
 */
const ACTION_KEYS = {
    EDIT: 'edit',
    DELETE: 'delete',
    TOGGLE: 'toggle',
}

/**
 * 返回套餐配置列表
 */
const handleBack = () => {
    emit('update:isDishType', false)
}

/**
 * 查询暂不点餐列表
 */
const fetchRuleList = async () => {
    state.loading = true
    try {
        const params = {
            ...state.form,
            pageNo: state.pagination.pageNo,
            pageSize: state.pagination.pageSize,
        }
        const { data } = await Http.post('/cloud/canteenNoOrderSetting/page', params)
        const { list = [], pageNo = 1, pageSize = state.pagination.pageSize, total = 0 } = data || {}
        state.cardList = list
        state.pagination.pageNo = pageNo
        state.pagination.pageSize = pageSize
        state.pagination.total = total
    } catch (error) {
        console.error('fetchRuleList error: ', error)
        state.cardList = []
    } finally {
        state.loading = false
    }
}

/**
 * 查询条件变化时重新获取数据
 */
const queryInitData = () => {
    state.pagination.pageNo = 1
    fetchRuleList()
}

/**
 * 翻页事件
 * @param {number} page 当前页码
 */
const handlePageChange = page => {
    state.pagination.pageNo = page
    fetchRuleList()
}

/**
 * 每页条数调整
 * @param {number} page 当前页码
 * @param {number} pageSize 每页条数
 */
const handlePageSizeChange = (page, pageSize) => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = pageSize
    fetchRuleList()
}

/**
 * 计算弹窗中需要展示的操作按钮
 * @param {NoOrderRule} rule 当前规则
 * @returns {{key: string, label: string}[]} 操作集合
 */
const getActionList = rule => {
    return [
        { key: ACTION_KEYS.EDIT, label: '编辑' },
        { key: ACTION_KEYS.DELETE, label: '删除' },
        { key: ACTION_KEYS.TOGGLE, label: rule.status ? '禁用' : '启用' },
    ]
}

/**
 * 统一处理卡片操作
 * @param {string} action 操作类型
 * @param {NoOrderRule} record 当前规则
 */
const handleCardAction = (action, record) => {
    switch (action) {
        case ACTION_KEYS.EDIT:
            editorProps(record)
            break
        case ACTION_KEYS.DELETE:
            handleCardDelete(record)
            break
        case ACTION_KEYS.TOGGLE:
            handleCardToggle(record)
            break
        default:
            break
    }
}

/**
 * 删除规则
 * @param {NoOrderRule} record 当前规则
 */
const handleCardDelete = record => {
    yConfirm('删除规则', '删除后不可恢复，确定删除该规则吗？', '删 除', '取 消').then(res => {
        if (!res) return
        Http.get('/cloud/canteenNoOrderSetting/delete', {
            id: record.id,
        }).then(res => {
            fetchRuleList()
        })
        YMessage.success('删除成功')
    })
}

/**
 * 启用/禁用规则
 * @param {NoOrderRule} record 当前规则
 */
const handleCardToggle = record => {
    Http.post('/cloud/canteenNoOrderSetting/updateStatus', {
        id: record.id,
        status: !record.status,
    }).then(res => {
        YMessage.success('操作成功')
        fetchRuleList()
    })
}

const handleShowPeopleInfo = async item => {
    if (item.personnelNames) {
        const { data } = await Http.get('/cloud/canteenNoOrderSetting/get', {
            id: item.id,
        })

        const people = data.personnelList
        peopleInfoModalRef.value?.showeModal(people)
    }
}

onMounted(() => {
    fetchRuleList()
})
</script>

<style lang="less" scoped>
.detail-package {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;

            .back {
                font-size: 16px;
            }
        }
    }

    .pageBody {
        padding: 20px;
        height: calc(100vh - 160px);
        overflow-y: auto;
    }
}
.create-btn {
    text-align: right;
    margin-bottom: 20px;
    margin-top: 20px;
}
.card_list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    min-height: 180px;

    .card-item {
        flex: 0 0 320px;
        background: #f9fafc;
        border: 1px solid @border-color-base;
        border-radius: 8px;
        padding: 18px 20px;
        position: relative;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.04);

        .card-item__header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f1f1f;
            max-width: 240px;
            line-height: 22px;
        }

        .card-more {
            font-size: 18px;
            cursor: pointer;
            padding: 2px;
            border-radius: 4px;
            transition: all 0.2s ease;

            &:hover {
                color: @primary-color;
                background: rgba(64, 128, 255, 0.1);
            }
        }

        .card-item__row {
            display: flex;
            font-size: 14px;
            color: #1f1f1f;
            line-height: 24px;
            margin-bottom: 6px;

            &--people {
                align-items: flex-start;
            }
        }

        .card-label {
            flex: 0 0 90px;
            color: #8c8c8c;
        }

        .card-value {
            flex: 1;
            color: #262626;
            word-break: break-all;
        }

        .card-people {
            display: flex;
            flex-wrap: wrap;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 90px;
            gap: 4px 12px;
        }

        .card-person {
            color: @primary-color;
        }

        .card-status {
            font-weight: 600;

            &--enabled {
                color: #52c41a;
            }

            &--disabled {
                color: #f5222d;
            }
        }
    }

    .card-empty {
        width: 100%;

        :deep(.tips) {
            color: #8c8c8c;
        }
    }
}
.card-pagination {
    margin-top: 16px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
}

.card-actions {
    display: flex;
    flex-direction: column;

    padding: 8px 0;
}

.card-actions__item {
    padding: 4px 16px;
    transition:
        background-color 0.2s ease,
        color 0.2s ease;
    cursor: pointer;
    &:hover {
        background: rgba(64, 128, 255, 0.1);
        color: @primary-color;
    }
}
</style>
