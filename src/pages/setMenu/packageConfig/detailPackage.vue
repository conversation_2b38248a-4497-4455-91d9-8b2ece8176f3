<template>
    <div class="detail-package">
        <div class="pageHead">
            <div class="pageHead_title">
                <ArrowLeftOutlined class="back" @click="handleBack" :style="{ color: store.sysColor }" />
                套餐详情
            </div>
        </div>
        <div class="pageBody">
            <a-form :model="state.formState" ref="formRef" layout="vertical" :rules="merchantFormRule">
                <a-form-item label="套餐名称：" name="name">
                    <a-input
                        v-model:value.trim="state.formState.name"
                        show-count
                        :maxlength="20"
                        placeholder="请输入"
                        :disabled="isDisabled"
                    />
                </a-form-item>

                <a-form-item label="套餐类型：" name="mealSetTypeName">
                    <a-input
                        v-model:value.trim="state.formState.mealSetTypeName"
                        show-count
                        :maxlength="20"
                        placeholder="请输入"
                        :disabled="isDisabled"
                    />
                </a-form-item>

                <a-form-item label="已选菜品：" name="mealSetDishes">
                    <div class="selected-dishes">
                        <template v-for="it in state.formState.mealSetDishes">
                            <div class="selected-dishes-item" v-if="!it.isSingleDish" :key="it.dishId">
                                <a-avatar shape="square" :size="86" :src="it.dishLogo"></a-avatar>
                                <div class="selected-dishes-item-name">{{ it.dishName }}</div>
                                <div class="selected-dishes-item-price">￥{{ it.dishPrice }}</div>
                            </div>
                        </template>
                    </div>
                </a-form-item>
                <a-form-item label="自选额外菜品：" name="mealSetDishes">
                    <div class="selected-dishes">
                        <template v-for="it in state.formState.mealSetDishes">
                            <div class="selected-dishes-item" v-if="it.isSingleDish" :key="it.dishId">
                                <a-avatar shape="square" :size="86" :src="it.dishLogo"></a-avatar>
                                <div class="selected-dishes-item-name">{{ it.dishName }}</div>
                                <div class="selected-dishes-item-price">￥{{ it.dishPrice }}</div>
                            </div>
                        </template>
                    </div>
                </a-form-item>
                <a-form-item label="套餐价格：" name="price">
                    <a-input-number
                        style="width: 100%"
                        :step="0.01"
                        string-mode
                        v-model:value.trim="state.formState.price"
                        placeholder="请输入价格（保留两位小数点，如9.99）"
                        addon-after="元"
                        :disabled="isDisabled"
                    ></a-input-number>
                </a-form-item>
                <!-- <a-form-item label="就餐状态：" name="dishType">
                    <p class="meal-status">{{ state.formState.orderDate }} 已就餐</p>
                </a-form-item> -->
            </a-form>
        </div>
    </div>
</template>
<script setup>
import { reactive, onMounted } from 'vue'
import Http from '@/utils/http.js'
const store = useStore()
const merchantFormRule = {
    name: [{ required: true, message: '请输入' }],
    dishImage: [{ required: true, trigger: ['change', 'blur'], message: '请选择' }],
    mealSetTypeName: [{ required: true, message: '请选择' }],
    dishJg: [{ required: true, message: '请输入' }],
    dishstatus: [{ required: true, message: '请选择' }],
    dishList: [{ required: true, message: '请选择' }],
}
const emit = defineEmits(['update:isDishType'])
const props = defineProps({
    isDishType: {
        type: Boolean,
        default: false,
    },
    isDisabled: {
        type: Boolean,
        default: true,
    },
    paramsEdit: {
        type: Object,
        default: () => ({}),
    },
})
const state = reactive({
    modalLoading: false,
    editDishTypeOpen: false,
    formState: {
        id: '',
        name: null,
        mealSetTypeName: null,
        mealSetDishes: [],

        dishstatus: 1,
        dishType: '',
    },
    searchForm: {
        form: {
            machineName: '',
            machineStatus: null,
        },
        dishList: [],
    },
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
})
// 单个套餐详情
const initPage = () => {
    Http.get('/cloud/canteenMealSet/get', { id: props.paramsEdit.id }).then(({ data }) => {
        state.formState = data
    })
}

// 返回
const handleBack = () => {
    emit('update:isDishType', false)
}
onMounted(() => {
    initPage()
})
</script>

<style lang="less" scoped>
.detail-package {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;

            .back {
                font-size: 16px;
            }
        }
    }

    .pageBody {
        padding: 20px;
        width: 68%;
        margin: 0 auto;

        :deep(.ant-form-item) {
            margin-bottom: 20px;
        }

        .selected-dishes {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;

            .selected-dishes-item {
                width: 100px;

                .selected-dishes-item-name {
                    // 超出1行省略号
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .selected-dishes-item-price {
                    color: #999999ff;
                }
            }
        }

        .meal-status {
            color: @primary-color;
        }
    }
}
</style>
