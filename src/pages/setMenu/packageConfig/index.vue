<!-- 套餐 -->
<template>
    <div class="set-menu">
        <template v-if="!state.isDishType">
            <div class="pageHead">
                <div class="pageHead_title">套餐管理</div>
            </div>
            <div class="pageBody">
                <div class="create-btn">
                    <a-button @click="handleImport">套餐导入</a-button>
                    <a-button v-auth="'canteenMachine.setMenu.reuse'" @click="state.packageReuseOpen = true">套餐复用</a-button>
                    <a-button v-auth="'canteenMachine.setMenu.set'" @click="state.packageSetOpen = true">订餐设置</a-button>
                    <a-button @click="handlerNoOrder">暂不点餐</a-button>
                    <a-button v-auth="'canteenMachine.setMenu.typeSet'" @click="handlerPackageTypeSet">套餐类型设置</a-button>
                </div>
                <div class="week-tabs">
                    <div class="week-btn">
                        <a-button @click="handlerWeekSwitch('last')">上周</a-button>
                        <a-range-picker
                            class="reset-picker"
                            format="YYYY-MM-DD"
                            valueFormat="YYYY-MM-DD"
                            disabled
                            v-model:value="state.query.weekStartEndDate"
                            picker="week"
                        />
                        <a-button @click="handlerWeekSwitch('next')">下周</a-button>
                        <a-button @click="handlerWeekSwitch('returnThisWeek')">返回本周</a-button>
                    </div>
                    <div class="set-btn">
                        <div class="label">
                            <label>预定套餐：</label>
                            <a-radio-group v-model:value="state.query.enabled">
                                <a-radio :value="true">开启</a-radio>
                                <a-radio :value="false">关闭</a-radio>
                            </a-radio-group>
                        </div>
                        <a-button type="primary" @click="handlerSave">确定</a-button>
                    </div>
                </div>
                <div class="week-calendar-table">
                    <template v-for="item in state.mealSetTypes" :key="item.mealSetTypeCode">
                        <div class="week-calendar-table-item" v-if="item.mealSetTypeCode === 'mealTimes'">
                            <div class="calendar-header meal"></div>
                            <div class="calendar-header" v-for="wek in state.calendarWeeks" :key="wek.value">
                                <div class="week">{{ wek.week }}</div>
                                <div class="date">{{ wek.dataValue }}</div>
                            </div>
                        </div>
                        <div class="week-calendar-table-item" v-else>
                            <div class="calendar-item meal">{{ item.mealSetTypeName }}</div>
                            <div
                                class="calendar-item"
                                v-for="week in state.calendarWeeks"
                                :class="{
                                    calendarItemEidt: !state.isPackage,
                                    calendarItemActive: state.weekActive == week.date + item.mealSetTypeCode,
                                }"
                                :key="week.value"
                            >
                                <div
                                    class="dish-name-container"
                                    :class="{ dishNameContainerActive: !isAddDish(week.date) }"
                                    @click="state.weekActive = week.date + item.mealSetTypeCode"
                                >
                                    <div class="dish-name-container-list" v-for="it in item.mealSets" :key="it.id">
                                        <div class="dish-name-item" v-if="week.date == it.orderDate">
                                            <a-popover placement="rightTop">
                                                <template #title>
                                                    <span :style="popoverStyle.popovertTitle">{{ it.name }}</span>
                                                </template>
                                                <template #content>
                                                    <div style="height: 200px; overflow: hidden auto; padding-bottom: 10px">
                                                        <template v-if="mealSetDishesComput(it).selected.length">
                                                            <div :style="popoverStyle.popoverItemTitle">已选菜品：</div>
                                                            <div
                                                                :style="popoverStyle.popoverContentItem"
                                                                v-for="j in mealSetDishesComput(it).selected"
                                                                :key="j.dishId"
                                                            >
                                                                <a-avatar shape="square" :size="50" :src="j.dishLogo">
                                                                    <template #icon>
                                                                        <UserOutlined />
                                                                    </template>
                                                                </a-avatar>
                                                                <div :style="popoverStyle.popoverContentItemRight">
                                                                    <p :style="popoverStyle.popoverContentItemRightTitle">
                                                                        {{ j.dishName }}
                                                                    </p>
                                                                    <p :style="popoverStyle.popoverContentItemRightPricet">
                                                                        ￥{{ j.dishPrice }}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </template>
                                                        <template v-if="mealSetDishesComput(it).additional.length">
                                                            <div :style="popoverStyle.popoverItemTitle" style="color: #999">
                                                                自选额外菜品：
                                                            </div>
                                                            <div
                                                                :style="popoverStyle.popoverContentItem"
                                                                v-for="j in mealSetDishesComput(it).additional"
                                                                :key="j.dishId"
                                                            >
                                                                <a-avatar shape="square" :size="50" :src="j.dishLogo">
                                                                    <template #icon>
                                                                        <UserOutlined />
                                                                    </template>
                                                                </a-avatar>
                                                                <div :style="popoverStyle.popoverContentItemRight">
                                                                    <p :style="popoverStyle.popoverContentItemRightTitle">
                                                                        {{ j.dishName }}
                                                                    </p>
                                                                    <p :style="popoverStyle.popoverContentItemRightPricet">
                                                                        ￥{{ j.dishPrice }}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </template>
                                                    </div>
                                                    <div :style="popoverStyle.popoverFooter">合计：{{ it.price }}元</div>
                                                </template>

                                                <div class="popover-title" @click="handlerDetail(it)">
                                                    <span class="defaultStatus" v-if="it.defaultStatus && state.isPackage">
                                                        默
                                                    </span>
                                                    <span>{{ it.name }}</span>
                                                </div>
                                            </a-popover>
                                        </div>
                                        <div class="dish-name-item operate" v-if="week.date == it.orderDate && !state.isPackage">
                                            <span>
                                                <FormOutlined class="edit-icon" @click="handlerAddDish(it)" />
                                                <DeleteOutlined class="delete-icon" @click="handlerDelete(it.id)" />
                                            </span>
                                            <a-switch
                                                v-model:checked="it.defaultStatus"
                                                size="small"
                                                @change="handlerSwitch(it)"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div
                                    class="add-dish"
                                    v-if="!state.isPackage && isAddDish(week.date)"
                                    @click="handlerAddDish(week, item.mealSetTypeCode)"
                                >
                                    添加套餐
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </template>
        <component
            v-else
            :is="state.components"
            v-model:isDishType="state.isDishType"
            :paramsEdit="state.paramsEdit"
            :orderDate="state.orderDate"
            @emitInitData="initPage"
        />

        <PackageSetModal v-model:open="state.packageSetOpen" />
        <PackageReuseModal v-model:open="state.packageReuseOpen" :currentWeekly="state.currentWeekly" @emitInitPage="initPage" />
        <YImport
            v-model:show="state.batchOpen"
            code="mealSetColumn"
            title="套餐导入"
            :importType="9"
            uploadUrl="/cloud/common/import"
            :progressRequest="progressRequest"
            :errorExport="errorExport"
            @cancel="cancelBatchImport"
        ></YImport>
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import dayjs from 'dayjs'
import Http from '@/utils/http.js'
import PackageSetModal from './packageSetModal.vue'
import PackageReuseModal from './packageReuseModal.vue'

const AddPackage = defineAsyncComponent(() => import('./addPackage.vue'))
const DetailPackage = defineAsyncComponent(() => import('./detailPackage.vue'))
const PackageType = defineAsyncComponent(() => import('./packageType.vue'))
// 暂不点餐
const noOrder = defineAsyncComponent(() => import('./noOrder.vue'))

const popoverStyle = {
    popovertTitle: {
        width: '200px',
        // 超出2行省略号
        display: '-webkit-box',
        webkitBoxOrient: 'vertical',
        webkitLineClamp: 1,
        overflow: 'hidden',
    },
    popoverItemTitle: {
        fontSize: '14px',
        color: '#000000FF',
    },
    popoverContentItem: {
        display: 'flex',
        alignTtems: 'center',
        marginTop: '8px',
    },
    popoverContentItemRight: {
        marginLeft: '10px',
    },
    popoverContentItemRightTitle: {
        width: '140px',
        fontSize: '12px',
        // 超出2行省略号
        display: '-webkit-box',
        webkitBoxOrient: 'vertical',
        webkitLineClamp: 2,
        overflow: 'hidden',
    },
    popoverContentItemRightPricet: {
        fontSize: '12px',
        color: '#00000033',
    },
    popoverFooter: {
        borderTop: '1px solid #e8e8e8',
        textAlign: 'right',
        padding: '10px 0 0',
        fontSize: '12px',
        color: '#666666FF',
    },
}
const state = reactive({
    batchOpen: false,
    importTitle: '',
    packageSetOpen: false,
    packageReuseOpen: false,
    components: null,
    isDishType: false,
    popconfirmOpen: true,
    weekActive: '',
    setMemuId: '',
    isPackage: false,
    query: {
        enabled: false,
        _enabled: false,
    },
    calendarWeeks: [],
    mealSetTypes: [],
    currentWeekly: {
        startDate: '',
        endDate: '',
    },
})
const initPage = () => {
    const [weekStartDate, weekEndDate] = state.query.weekStartEndDate
    const params = {
        isNextWeek: false,
        weekStartDate,
        weekEndDate,
    }
    Http.post('/cloud/canteenMealSet/listByWeekly', params).then(({ data }) => {
        state.calendarWeeks = data.weeks.map((item, i) => {
            return {
                ...item,
                dataValue: dayjs(item.date).format('DD'),
            }
        })
        state.mealSetTypes = [
            {
                mealSetTypeName: '',
                mealSetTypeCode: 'mealTimes',
                mealSets: [],
            },
            ...data.mealSetTypes,
        ]
    })
}
// 点击添加套餐
const handlerAddDish = (item, it) => {
    state.isDishType = true
    state.paramsEdit = item || {}
    // 添加套餐时把餐次带过去
    if (it) {
        state.paramsEdit.mealSetTypeCode = it
    }
    state.orderDate = item.date || item.orderDate
    state.components = AddPackage
}
// 删除菜品
const handlerDelete = id => {
    Http.post('/cloud/canteenMealSet/delete', { id }).then(({ message }) => {
        YMessage.success(message)
        initPage()
    })
}
// 查看详情
const handlerDetail = item => {
    state.isDishType = true
    state.paramsEdit = item
    state.components = DetailPackage
}
// 默认配置菜品
const handlerSwitch = it => {
    Http.post('/cloud/canteenMealSet/updateDefaultStatus', it)
        .then(({ message }) => {
            YMessage.success(message)
            initPage()
        })
        .catch(error => {
            it.defaultStatus = !it.defaultStatus
        })
}

// 套餐导入
const handleImport = () => {
    state.batchOpen = true
    state.importTitle = '套餐导入'
}

// 请求进度条数据
async function progressRequest(importId) {
    const { data } = await Http.post(`/cloud/common/import/progress`, { importId })
    return data
}

// 异常数据下载
async function errorExport(importIds) {
    const params = { "importId": importIds[0], "importType": 9 }
    Http.download(`/cloud/common/export/importErrorLog`, 'post', params, '套餐导入异常数据.xlsx')
}

// 取消批量导入
function cancelBatchImport() {
    state.batchOpen = false
}

// 套餐类型设置
const handlerPackageTypeSet = () => {
    state.isDishType = true
    state.components = PackageType
}

// 暂不点餐
const handlerNoOrder = () => {
    state.isDishType = true
    state.components = noOrder
}

// 上下周切换
const handlerWeekSwitch = item => {
    // 获取当前周的上一周
    let firstDayOfCurrentWeek = ''
    // 获取当前周的最后一天
    let lastDayOfCurrentWeek = ''
    // 上一周
    const [weekStartDate, weekEndDate] = state.query.weekStartEndDate
    if (item === 'last') {
        firstDayOfCurrentWeek = dayjs(weekStartDate).subtract(1, 'week').format('YYYY-MM-DD')
        lastDayOfCurrentWeek = dayjs(weekEndDate).subtract(1, 'week').format('YYYY-MM-DD')
    } else if (item === 'next') {
        // 下一周
        firstDayOfCurrentWeek = dayjs(weekStartDate).add(1, 'week').format('YYYY-MM-DD')
        lastDayOfCurrentWeek = dayjs(weekEndDate).add(1, 'week').format('YYYY-MM-DD')
    } else {
        // 本周
        firstDayOfCurrentWeek = dayjs().weekday(1).format('YYYY-MM-DD')
        lastDayOfCurrentWeek = dayjs().weekday(7).format('YYYY-MM-DD')
    }
    state.query.weekStartEndDate = [firstDayOfCurrentWeek, lastDayOfCurrentWeek]
    state.currentWeekly.startDate = firstDayOfCurrentWeek
    state.currentWeekly.endDate = lastDayOfCurrentWeek
    initPage()
    handlerPackageStatus()
}
// 预定套餐：开启/关闭
const handlerPackageStatus = () => {
    const [startDate, endDate] = state.query.weekStartEndDate
    const params = { startDate, endDate }
    Http.post('/cloud/canteenWeeklyOrderSetting/get', params).then(({ data }) => {
        state.isPackage = data.enabled
        state.query.enabled = data.enabled
        state.query._enabled = data.enabled
    })
}
const updatePackageStatus = () => {
    const [startDate, endDate] = state.query.weekStartEndDate
    const params = { startDate, endDate, enabled: state.query.enabled }
    Http.post('/cloud/canteenWeeklyOrderSetting/update', params).then(({ data, message }) => {
        YMessage.success(message)
        handlerPackageStatus()
    })
}
// 更新 预定套餐：开启/关闭
const handlerSave = () => {
    if (state.query.enabled) {
        updatePackageStatus()
    } else {
        yConfirm('预定套餐', '关闭后移动端不展示，用户无法订餐', '确 认', '取 消').then(res => {
            if (res) {
                updatePackageStatus()
            } else {
                state.query._enabled && (state.query.enabled = true)
            }
        })
    }
}

const isAddDish = computed(() => {
    return date => {
        return dayjs(date).format('YYYY-MM-DD') >= dayjs().format('YYYY-MM-DD')
    }
})
// 处理菜单详情
const mealSetDishesComput = computed(() => {
    return data => {
        const obj = {
            additional: [], // 额外菜品
            selected: [], // 已选菜品
        }
        data.mealSetDishes?.forEach(v => {
            if (v.isSingleDish) {
                obj.additional.push(v)
            } else {
                obj.selected.push(v)
            }
        })
        return obj
    }
})
onMounted(() => {
    // 获取当前周从周二开始的第一天
    const firstDayOfCurrentWeek = dayjs().weekday(1)
    // // 获取当前周的最后一天（以周二为起始，最后一天是周一）
    const lastDayOfCurrentWeek = dayjs().weekday(7)
    // // .add(1, 'week')
    const startDate = firstDayOfCurrentWeek.format('YYYY-MM-DD')
    const endDate = lastDayOfCurrentWeek.format('YYYY-MM-DD')
    state.query.weekStartEndDate = [startDate, endDate]
    state.currentWeekly.startDate = startDate
    state.currentWeekly.endDate = endDate
    initPage()
    handlerPackageStatus()
})
</script>

<style lang="less" scoped>
@color65: #656566ff;

.set-menu {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;
        }
    }

    .pageBody {
        padding: 20px;

        .create-btn {
            text-align: right;
        }

        .week-tabs {
            padding: 20px 0;
            display: flex;
            justify-content: space-between;
        }

        .reset-picker {
            margin: 0 10px;
            background-color: @body-background;
            :deep(input) {
                color: @text-color;
            }
        }

        .week-btn,
        .set-btn {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .week-calendar-table {
            width: 100%;
            overflow-x: auto;

            .week-calendar-table-item {
                display: flex;

                .calendar-item,
                .calendar-header {
                    width: 214px;
                    min-width: 214px;

                    &.meal {
                        width: 134px;
                        min-width: 134px;
                        position: flex;
                        left: 0;
                    }
                    &:hover {
                        .dish-name-container {
                            background-color: @acitve-background;

                            .title {
                                cursor: pointer;

                                &:hover {
                                    color: @primary-color;
                                }
                            }
                        }
                    }
                }

                .calendar-header {
                    height: 100px;
                    line-height: 70px;
                    display: flex;
                    text-align: center;
                    flex-direction: column;
                    border: 1px solid @border-color-base;

                    &:not(:last-child) {
                        border-right: none;
                    }

                    .week {
                        height: 30px;
                        color: #999999ff;
                        font-size: 14px;
                    }

                    .date {
                        height: 22px;
                        color: @color65;
                        font-size: 24px;
                        font-weight: 600;
                    }
                }

                .calendar-item {
                    border: 1px solid @border-color-base;
                    border-top: none;
                    border-right: none;

                    &:last-child {
                        border: 1px solid @border-color-base;
                        border-top: none;
                    }

                    &.meal {
                        text-align: center;
                        line-height: 170px;
                        height: 170px;
                        font-size: 15px;
                        font-weight: 600;
                        width: 134px;
                    }

                    .dish-name-container {
                        overflow: hidden auto;
                        height: 169px;

                        .dish-name-item {
                            padding: 6px 12px;
                            color: #595959ff;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            &.operate {
                                margin: 0 12px;
                                padding: 0 0 6px 0;
                                border-bottom: 1px solid @border-color-base;
                            }

                            .popover-title {
                                cursor: pointer;
                                // 字数超出隐藏
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;

                                &:hover {
                                    color: @primary-color;
                                }

                                .defaultStatus {
                                    background-color: @primary-color;
                                    color: @body-background;
                                    padding: 2px 4px;
                                    border-radius: 2px;
                                    margin-right: 4px;
                                    font-size: 12px;
                                }
                            }

                            .edit-icon {
                                color: @primary-color;
                                margin-right: 15px;
                            }

                            .delete-icon {
                                color: @error-color;
                            }
                        }
                    }

                    .add-dish {
                        text-align: center;
                        line-height: 30px;
                        height: 30px;
                        color: @primary-color;
                        cursor: pointer;
                    }

                    &.calendarItemEidt {
                        .dish-name-container {
                            height: 138px;
                        }
                    }

                    &.calendarItemActive {
                        border: 1px solid @primary-color;

                        .dish-name-container {
                            background-color: @acitve-background;

                            .title {
                                cursor: pointer;

                                &:hover {
                                    color: @primary-color;
                                }
                            }
                        }
                    }

                    .dishNameContainerActive {
                        height: 169px !important;
                    }
                }
            }
        }
    }
}

.modal-rest-form {
    display: flex;
    align-items: center;
    margin: 0;

    :deep(.ant-form-item-explain) {
        position: absolute;
    }
}
</style>
