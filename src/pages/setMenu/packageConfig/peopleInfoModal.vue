<template>
    <YModal v-model:open="state.visible" title="人员信息" width="600px" :footer="null" :maskClosable="true" @cancel="handleClose">
        <div class="people-info-modal">
            <div class="type-select">
                <a-radio-group @change="handleTypeChange" v-model:value="state.selectedType" button-style="solid">
                    <a-radio-button value="student">学生</a-radio-button>
                    <a-radio-button value="teacher">老师</a-radio-button>
                </a-radio-group>
            </div>

            <div class="search-area">
                <div class="search-row">
                    <span class="search-label">姓名：</span>
                    <a-input
                        v-model:value="state.searchName"
                        placeholder="请输入"
                        class="search-input"
                        @pressEnter="handleSearch"
                    />
                    <div class="search-buttons">
                        <a-button type="primary" @click="handleSearch">
                            <SearchOutlined />
                            查询
                        </a-button>
                        <a-button @click="handleReset">
                            <SyncOutlined />
                            重置
                        </a-button>
                    </div>
                </div>
            </div>

            <div class="people-content">
                <a-spin :spinning="state.loading">
                    <template v-if="state.filteredPeople.length">
                        <div class="people-tags">
                            <div
                                v-for="(person, index) in state.filteredPeople"
                                :key="index"
                                class="people-tag"
                                :class="`people-tag--${person.type}`"
                            >
                                <span class="people-tag__name">{{ person.name }}</span>
                                <span class="people-tag__type">{{ getTypeLabel(person.type) }}</span>
                            </div>
                        </div>
                    </template>
                    <Empty v-else tips="暂无人员信息" />
                </a-spin>
            </div>
        </div>
    </YModal>
</template>

<script setup>
import { reactive, computed } from 'vue'
import { SearchOutlined, SyncOutlined } from '@ant-design/icons-vue'
import YModal from '@/components/common/YModal/index.vue'
import Empty from '@/components/common/Empty.vue'

/**
 * 组件状态
 */
const state = reactive({
    selectedType: 'student',
    visible: false,
    loading: false,
    searchName: '',
    peopleList: [],
    filteredPeople: [],
})

/**
 * 人员类型映射
 */
const TYPE_MAP = {
    student: '学生',
    teacher: '老师',
}

/**
 * 获取类型标签文本
 * @param {string} type - 人员类型
 * @returns {string} 类型文本
 */
const getTypeLabel = type => {
    return TYPE_MAP[type] || '未知'
}

/**
 * 打开弹窗并加载人员数据
 * @param {Array} peopleData - 人员数据数组
 */
const showeModal = (peopleData = []) => {
    state.visible = true
    state.searchName = ''

    state.peopleList = peopleData.map((item, index) => ({
        name: item.personnelName,
        type: item.personnelType ? 'teacher' : 'student', // 示例：交替显示学生和老师
    }))
    state.filteredPeople = state.peopleList.filter(person => person.type === state.selectedType)
}

/**
 * 搜索人员
 */
const handleSearch = () => {
    const keyword = state.searchName.trim().toLowerCase()
    if (!keyword) {
        state.filteredPeople = state.peopleList.filter(person => person.type === state.selectedType)
        return
    }

    const filteredPeople = (state.filteredPeople = state.peopleList.filter(person => person.type === state.selectedType))

    state.filteredPeople = filteredPeople.filter(person => person.name.toLowerCase().includes(keyword))
}

/**
 * 类型切换
 */
const handleTypeChange = () => {
    state.filteredPeople = state.peopleList.filter(person => person.type === state.selectedType)
}

/**
 * 重置搜索
 */
const handleReset = () => {
    state.searchName = ''
    state.filteredPeople = state.peopleList.filter(person => person.type === state.selectedType)
}

/**
 * 关闭弹窗
 */
const handleClose = () => {
    state.visible = false
    state.searchName = ''
    state.peopleList = []
    state.filteredPeople = []
}

/**
 * 暴露给父组件的方法
 */
defineExpose({
    showeModal,
})
</script>

<style lang="less" scoped>
.people-info-modal {
    padding: 20px;
    .search-area {
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
    }

    .search-row {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .search-label {
        flex-shrink: 0;
        font-size: 14px;
        color: #262626;
    }

    .search-input {
        flex: 1;
        max-width: 240px;
    }

    .search-buttons {
        display: flex;
        gap: 8px;
        margin-left: auto;

        .ant-btn {
            display: flex;
            align-items: center;
            gap: 4px;
        }
    }

    .people-content {
        min-height: 200px;
        max-height: 400px;
        overflow-y: auto;
    }

    .people-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
    }

    .people-tag {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 14px;
        background: #f5f5f5;
        border: 1px solid #d9d9d9;
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &--student {
            background: #e6f7ff;
            border-color: #91d5ff;

            .people-tag__type {
                color: #1890ff;
                background: #bae7ff;
            }
        }

        &--teacher {
            background: #fff7e6;
            border-color: #ffd591;

            .people-tag__type {
                color: #fa8c16;
                background: #ffe7ba;
            }
        }
    }

    .people-tag__name {
        color: #262626;
        font-weight: 500;
    }

    .people-tag__type {
        padding: 2px 8px;
        border-radius: 2px;
        font-size: 12px;
        font-weight: normal;
    }
}

.type-select {
    padding-bottom: 16px;
}
</style>
