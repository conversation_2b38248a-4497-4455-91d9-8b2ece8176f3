<template>
    <YDrawer
        :width="500"
        v-model:open="state.open"
        :mask="true"
        :title="state.title"
        @close="cancel"
        getContainer="body"
        :rootStyle="{
            height: '100vh',
            top: '0px',
        }"
    >
        <div class="create-form">
            <a-form :model="state.formState" ref="formRef" layout="vertical" :rules="merchantFormRule">
                <a-form-item label="规则名称：" name="name">
                    <a-input v-model:value.trim="state.formState.name" show-count :maxlength="20" placeholder="请输入" />
                </a-form-item>
                <a-form-item label="人员信息：" name="personnelName">
                    <a-input
                        v-model:value.trim="state.formState.personnelName"
                        readonly
                        @click="() => (state.selectOpen = true)"
                        placeholder="请选择"
                    />
                </a-form-item>
                <a-form-item label="不点套餐类型：" name="mealSetTypeIds">
                    <a-select
                        mode="multiple"
                        v-model:value="state.formState.mealSetTypeIds"
                        style="width: 100%"
                        placeholder="请选择"
                    >
                        <a-select-option v-for="it in state.modelOptions" :key="it.id" :value="it.id">
                            {{ it.name }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="时间范围：" name="range">
                    <a-range-picker v-model:value="state.formState.range" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
                </a-form-item>
            </a-form>
        </div>
        <template #footer>
            <a-button @click="cancel">取消</a-button>
            <a-button type="primary" :loading="state.loading" @click="submit">确定</a-button>
        </template>
    </YDrawer>

    <YSelect
        ref="ySelectRef"
        v-model:visible="state.selectOpen"
        :tabs="selectTabs"
        :selected="state.selected"
        :isPickType="comIsPickType"
        @confirm="handerConfirm"
    />
</template>

<script setup>
import Http from '@/utils/http.js'
const ySelectRef = ref(null)

const comIsPickType = computed(() => {
    return ySelectRef.value?.activeTabIndex === 0 ? 'student' : 'people_dept'
})

const selectTabs = [
    {
        tab: '学生',
        key: 1,
        businessType: 11,
        code: null,
        single: false, // 单选多选
        checked: true,
    },
    {
        tab: '教职工',
        key: 2,
        businessType: 21,
        code: null,
        single: false, // 单选多选
        checked: true,
    },
]
const emit = defineEmits(['emitInitData', 'submitSuccess'])

const state = reactive({
    title: '添加',
    open: false,
    selected: [],
    selectOpen: false,
    modelOptions: [],
    loading: false,
    formState: {},
})

const merchantFormRule = {
    name: [{ required: true, message: '请输入' }],
    personnelName: [{ required: true, message: '请输入' }],
    mealSetTypeIds: [{ required: true, message: '请选择' }],
    range: [{ required: true, message: '请选择' }],
}

const formRef = ref(null)

const cancel = () => {
    state.formState = {}
    formRef.value.resetFields()
    state.open = false
    emit('submitSuccess')
}

const handerConfirm = data => {
    console.log(data, '123123123123123123')

    // 填充回显数据
    state.selected = data

    let personnelList = []

    personnelList = data.map(item => {
        return {
            personnelId: item.id,
            personnelName: item.name,
            personnelType: item.typeValue === 'student' ? 0 : 1,
        }
    })

    state.formState.personnelName = personnelList.map(item => item.personnelName).join(',') || ''
    state.formState.personnelList = personnelList
    state.selectOpen = false
}

function getMealSetType() {
    Http.get('/cloud/canteenMealSetType/get').then(res => {
        state.modelOptions = res.data
    })
}

const submit = () => {
    const [startDate, endDate] = state.formState.range
    const params = {
        ...state.formState,
        startDate,
        endDate,
    }

    if (state.formState.id) {
        Http.post('/cloud/canteenNoOrderSetting/update', params).then(({ message }) => {
            YMessage.success(message)
            cancel()
        })
    } else {
        Http.post('/cloud/canteenNoOrderSetting/create', params).then(({ message }) => {
            YMessage.success(message)
            cancel()
        })
    }
}

const showeModal = data => {
    state.open = true
    getMealSetType()
    if (data) {
        state.formState = data

        state.selected = state.formState.personnelList.map(item => {
            return {
                id: item.personnelId,
                name: item.personnelName,
                typeValue: item.personnelType === 0 ? 'student' : 'people_dept',
            }
        })
        state.title = '编辑'
    } else {
        state.formState = {}
        state.title = '添加'
    }
}
defineExpose({
    showeModal,
})
</script>

<style lang="less" scoped>
.create-form {
    :deep(.ant-form-item) {
        margin-bottom: 20px;
    }
}
</style>
