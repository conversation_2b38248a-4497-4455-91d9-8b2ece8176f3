<template>
    <div class="no-order-log">
        <div class="pageHead">
            <div class="pageHead_title">
                <ArrowLeftOutlined class="back" @click="handleBack" :style="{ color: store.sysColor }" />
                操作日志
            </div>
        </div>
        <div class="pageBody">
            <searchForm
                class="search-form"
                v-model:formState="state.form"
                :formList="formList"
                @submit="queryLogs"
                @reset="handleReset"
            ></searchForm>
            <ETable
                class="log-table"
                :columns="columns"
                :data-source="state.dataSource"
                :paginations="state.pagination"
                :loading="state.loading"
                @change="handleTableChange"
            >
                <template #bodyCell="{ column, record, index, text }">
                    <template v-if="column.dataIndex === 'index'">
                        {{ formatIndex(index, state.pagination.pageNo, state.pagination.pageSize) }}
                    </template>
                    <template v-else-if="column.dataIndex === 'status'">
                        <span :class="['status-label', record.status === '操作成功' ? 'is-success' : 'is-fail']">
                            {{ record.status }}
                        </span>
                    </template>
                    <template v-else>
                        <Tooltip :maxWidth="column.width" :title="text"></Tooltip>
                    </template>
                </template>
            </ETable>
        </div>
    </div>
</template>

<script setup>
import dayjs from 'dayjs'
const store = useStore()
const emit = defineEmits(['back'])

/**
 * 搜索表单配置
 */
const formList = ref([
    {
        type: 'input',
        value: 'account',
        label: '操作账号',
        span: 6,
    },
    {
        type: 'rangePicker',
        value: ['startTime', 'endTime'],
        label: '操作时间',
        span: 8,
    },
])

/**
 * 表格列配置
 */
const columns = [
    { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
    { title: '操作账号', dataIndex: 'account', width: 160 },
    { title: '设备IP', dataIndex: 'ip', width: 150 },
    { title: '操作模块', dataIndex: 'module', width: 160 },
    { title: '操作内容', dataIndex: 'content', width: 200 },
    { title: '更新时间', dataIndex: 'updatedAt', width: 180 },
    { title: '操作状态', dataIndex: 'status', width: 120 },
]

/**
 * @typedef {Object} OperationLog
 * @property {string} id 唯一标识
 * @property {string} account 操作账号
 * @property {string} ip 设备 IP
 * @property {string} module 操作模块
 * @property {string} content 操作内容
 * @property {string} updatedAt 更新时间（展示文案）
 * @property {string} status 操作状态
 */

/**
 * 操作日志模拟数据，后续可替换为接口返回
 * @type {OperationLog[]}
 */
const defaultLogs = [
    {
        id: 'log-001',
        account: 'admin_747016',
        ip: '***************',
        module: '菜品管理',
        content: '添加菜品',
        updatedAt: '2023.06.12 20:00:12',
        status: '操作成功',
    },
    {
        id: 'log-002',
        account: 'admin_747016',
        ip: '***************',
        module: '套餐配置',
        content: '删除不点餐人员',
        updatedAt: '2023.06.12 20:00:12',
        status: '操作成功',
    },
    {
        id: 'log-003',
        account: 'admin_888888',
        ip: '*********',
        module: '套餐配置',
        content: '禁用暂不点餐规则',
        updatedAt: '2023.07.02 17:30:45',
        status: '操作成功',
    },
    {
        id: 'log-004',
        account: 'admin_666001',
        ip: '*************',
        module: '菜品管理',
        content: '批量导入菜品',
        updatedAt: '2023.08.15 09:21:05',
        status: '操作失败',
    },
]

/**
 * 页面内部状态
 */
const state = reactive({
    loading: false,
    form: {
        account: null,
        startTime: null,
        endTime: null,
    },
    pagination: {
        total: 0,
        pageNo: 1,
        pageSize: 10,
    },
    allLogs: [],
    filteredLogs: [],
    dataSource: [],
})

/**
 * 返回暂不点餐页面
 */
const handleBack = () => {
    emit('back')
}

/**
 * 生成当前过滤条件下的日志列表
 * @returns {OperationLog[]} 过滤后的日志集合
 */
const filterLogs = () => {
    const accountKeyword = state.form.account ? state.form.account.trim().toLowerCase() : ''
    const startTimestamp = state.form.startTime
        ? dayjs(state.form.startTime, 'YYYY-MM-DD').startOf('day').valueOf()
        : null
    const endTimestamp = state.form.endTime ? dayjs(state.form.endTime, 'YYYY-MM-DD').endOf('day').valueOf() : null
    return state.allLogs.filter(log => {
        const matchAccount = accountKeyword ? log.account.toLowerCase().includes(accountKeyword) : true
        const logTimestamp = dayjs(log.updatedAt, 'YYYY.MM.DD HH:mm:ss').valueOf()
        const matchStart = startTimestamp ? logTimestamp >= startTimestamp : true
        const matchEnd = endTimestamp ? logTimestamp <= endTimestamp : true
        return matchAccount && matchStart && matchEnd
    })
}

/**
 * 根据分页信息刷新表格数据
 */
const updateDataSource = () => {
    const startIndex = (state.pagination.pageNo - 1) * state.pagination.pageSize
    const endIndex = startIndex + state.pagination.pageSize
    state.dataSource = state.filteredLogs.slice(startIndex, endIndex)
}

/**
 * 查询日志数据
 */
const queryLogs = () => {
    state.loading = true
    try {
        state.filteredLogs = filterLogs()
        state.pagination.total = state.filteredLogs.length
        state.pagination.pageNo = 1
        updateDataSource()
    } finally {
        state.loading = false
    }
}

/**
 * 表格分页变化
 * @param {Object} pagination antd 分页参数
 */
const handleTableChange = pagination => {
    state.pagination.pageNo = pagination.current
    state.pagination.pageSize = pagination.pageSize
    updateDataSource()
}

/**
 * 重置搜索条件
 */
const handleReset = () => {
    state.form.account = null
    state.form.startTime = null
    state.form.endTime = null
    queryLogs()
}

/**
 * 序号格式化，补零展示
 * @param {number} index 当前页索引
 * @param {number} pageNo 当前页
 * @param {number} pageSize 每页条数
 * @returns {string} 展示序号
 */
const formatIndex = (index, pageNo, pageSize) => {
    const order = (pageNo - 1) * pageSize + index + 1
    return order < 10 ? `0${order}` : `${order}`
}

/**
 * 初始化日志集合
 */
const initLogs = () => {
    state.allLogs = defaultLogs.map(item => ({ ...item }))
    queryLogs()
}

onMounted(() => {
    initLogs()
})
</script>

<style lang="less" scoped>
.no-order-log {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            font-style: normal;
        }

        .back {
            font-size: 16px;
            margin-right: 8px;
        }
    }

    .pageBody {
        padding: 20px;
    }

    .search-form {
        margin-bottom: 20px;
    }

    .log-table {
        background: #fff;
        border-radius: 8px;
        padding: 4px 0;
    }
}

.status-label {
    font-weight: 500;

    &.is-success {
        color: #52c41a;
    }

    &.is-fail {
        color: #f5222d;
    }
}
</style>
