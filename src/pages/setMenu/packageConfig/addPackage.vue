<template>
    <div class="addPackage">
        <div class="pageHead">
            <div class="pageHead_title">
                <ArrowLeftOutlined class="back" @click="handleBack" :style="{ color: store.sysColor }" />
                {{ paramsEdit.id ? '编辑套餐' : '添加套餐' }}
            </div>
        </div>
        <div class="pageBody">
            <a-form :model="state.formState" ref="addPackageRef" layout="vertical" :rules="merchantFormRule">
                <a-form-item label="套餐名称：" name="name">
                    <a-input
                        v-model:value.trim="state.formState.name"
                        show-count
                        :maxlength="20"
                        placeholder="请输入"
                        :disabled="isDisabled"
                    />
                </a-form-item>

                <a-form-item label="套餐类型：" name="mealSetTypeId">
                    <a-select
                        :disabled="isDisabled"
                        v-model:value="state.formState.mealSetTypeId"
                        style="width: 100%"
                        placeholder="请选择"
                    >
                        <a-select-option v-for="it in state.modelOptions" :key="it.id" :value="it.id">
                            {{ it.name }}
                        </a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="选择菜品：" name="dishsName">
                    <a-input
                        style="width: 100%"
                        v-model:value.trim="state.formState.dishsName"
                        placeholder=" 请选择"
                        :disabled="isDisabled"
                        @click="handlerDishOpen(false)"
                    ></a-input>
                </a-form-item>
                <a-form-item label="套餐价格：" name="price">
                    <a-input-number
                        style="width: 100%"
                        :step="0.01"
                        :min="0.0"
                        :max="9999"
                        string-mode
                        v-model:value.trim="state.formState.price"
                        placeholder="请输入价格（保留两位小数点，如9.99）"
                        addon-after="元"
                        :disabled="isDisabled"
                        @change="handlePriceChange"
                    ></a-input-number>
                </a-form-item>
                <a-form-item label="单点菜品：" name="dishName">
                    <a-input
                        style="width: 100%"
                        v-model:value.trim="state.formState.dishName"
                        placeholder=" 请选择"
                        :disabled="isDisabled"
                        @click="handlerDishOpen(true)"
                    ></a-input>
                </a-form-item>
            </a-form>
        </div>
        <div class="footer">
            <a-button @click="handleBack">取消</a-button>
            <a-button type="primary" :loading="state.loading" @click="handlePackageSubmit">确定</a-button>
        </div>

        <YModal
            v-model:open="state.editDishTypeOpen"
            :footer="false"
            title="选择菜品"
            @cancel="handleCancel"
            @ok="handleOk"
            width="900px"
        >
            <div class="select-pachage-content">
                <div class="pending-pool">
                    <searchForm
                        class="search-Form"
                        v-model:formState="state.searchForm.form"
                        :formList="formList"
                        @submit="queryInitData"
                        @reset="queryInitData"
                    >
                        <template #dishTypeId>
                            <a-select v-model:value="state.searchForm.form.dishTypeId" placeholder="请选择">
                                <a-select-option v-for="it in state.dishTypeList" :key="it.id" :value="it.id">
                                    {{ it.name }}
                                </a-select-option>
                            </a-select>
                        </template>
                    </searchForm>
                    <a-checkbox-group
                        class="columns-group"
                        v-model:value="state[state.isCheckboxRadio ? 'selectdDish' : 'selectdDishs']"
                        @change="checkboxDishList"
                        :disabled="isMaxLen"
                    >
                        <template v-for="item in state.menuList" :key="item.id">
                            <a-checkbox class="columns-group-checkbox" :value="item.id" v-show="!item.isShow">
                                <a-avatar shape="square" :size="86" :src="item.logo"></a-avatar>
                                <p class="title">{{ item.name }}</p>
                                <p class="price">￥ {{ item.price }}</p>
                            </a-checkbox>
                        </template>
                    </a-checkbox-group>
                    <Empty v-if="!state.menuList.length" :emptyStyle="{ padding: '100px' }"></Empty>
                </div>
                <div class="selected-pool">
                    <div class="selected-pool_title">
                        <span class="selected-dishes">已选菜品</span>
                        <span class="selected-num">{{ state.searchForm.dishList?.length }}</span>
                    </div>
                    <div class="selected-pool_list">
                        <div class="pool_list_item" v-for="item in state.searchForm.dishList" :key="item.id">
                            <span class="title">{{ item.name }}</span>
                            <span class="">
                                {{ item.price }}元
                                <DeleteOutlined class="delete-icon" @click="handlerDelete(item.id)" />
                            </span>
                        </div>
                        <Empty v-if="!state.searchForm.dishList.length" :emptyStyle="{ paddingTop: '100px' }"></Empty>
                    </div>
                    <dvi class="footer">
                        <a-button key="submit" type="primary" @click="handleOk">确定</a-button>
                    </dvi>
                </div>
            </div>
        </YModal>
    </div>
</template>

<script setup>
import { reactive, onMounted, shallowRef } from 'vue'
import Http from '@/utils/http.js'
const isMaxLen = computed(() => {
    return state[state.isCheckboxRadio ? 'selectdDish' : 'selectdDishs'].length >= 20
})
const store = useStore()
const formList = [
    {
        type: 'input',
        value: 'name',
        label: '菜品名称',
        span: 9,
    },
    {
        type: 'slot',
        value: 'dishTypeId',
        label: '菜品类型',
        list: [],
        span: 8,
    },
]
// const formRef = shallowRef(null)
const addPackageRef = shallowRef()
const emit = defineEmits(['update:isDishType', 'emitInitData'])
const props = defineProps({
    paramsEdit: {
        type: Object,
        default: () => ({}),
    },

    isDisabled: {
        // 是否禁用
        type: Boolean,
        default: false,
    },
    orderDate: {
        // 订单日期
        type: String,
        default: '',
    },
})
const state = reactive({
    selectdDish: [],
    selectdDishs: [],
    modalLoading: false,
    editDishTypeOpen: false,
    isCheckboxRadio: false,
    formState: {
        id: '',
        name: null,
        mealSetTypeId: null,
        // 多选菜单
        dishsName: null,
        dishs: [],
        // 单选菜单
        dish: [],
        dishName: null,
        mealSetDishes: [
            // {
            //     dishId: null,
            //     dishName: null,
            //     isSingleDish: false,
            // },
        ],
        price: null,
    },
    searchForm: {
        form: {
            name: '',
            dishTypeId: null,
        },
        dishList: [],
    },
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    modelOptions: [],
    menuList: [],
    dishTypeList: [],
    dishForm: {
        enabled: 1,
        pageNo: 1,
        pageSize: 500,
        total: 0,
    },
    loading: false,
})
const merchantFormRule = {
    name: [{ required: true, message: '请输入' }],
    mealSetTypeId: [{ required: true, message: '请选择' }],
    dishsName: [{ required: true, message: '请选择' }],
    price: [{ required: true, message: '请输入' }],
    isSingleDish: [{ required: true, message: '请选择' }],
}

const initPage = () => {
    Http.get('/cloud/canteenMealSet/get', { id: props.paramsEdit.id }).then(({ data }) => {
        let _dishName = []
        let _dishsName = []
        state.formState = { ...state.formState, ...data }
        data.mealSetDishes.forEach(item => {
            // 单点菜品 true：单点
            if (item.isSingleDish) {
                state.formState.dish.push(item)
                state.selectdDish.push(item.id)
                _dishName.push(item.dishName)
            } else {
                state.formState.dishs.push(item)
                state.selectdDishs.push(item.id)
                _dishsName.push(item.dishName)
            }
        })
        state.formState.dishName = _dishName.join(',') || ''
        state.formState.dishsName = _dishsName.join(',') || ''
    })
}

// 返回
const handleBack = () => {
    emit('update:isDishType', false)
}
// 确定 套餐管理-添加套餐
const handlePackageSubmit = () => {
    let Api = '/cloud/canteenMealSet/create'
    if (state.formState.id) {
        Api = '/cloud/canteenMealSet/update'
    }
    addPackageRef.value.validate().then(() => {
        const params = {
            ...state.formState,
            orderDate: props.orderDate,
        }

        params.mealSetDishes = [...params.dishs, ...params.dish]
        Http.post(Api, params)
            .then(res => {
                const { data, message } = res
                state.dataSource = data.mealSetTypes
                YMessage.success(message)
                handleBack()
                emit('emitInitData')
            })
            .catch(err => {
                YMessage.error(err.message)
            })
    })
}

// 查询重置
const queryInitData = status => {
    const { name, dishTypeId } = state.searchForm.form
    state.menuList.forEach(item => {
        if (status == null) {
            item.isShow = false
        } else {
            if (name || dishTypeId) {
                item.isShow = true
                item.isShow = !(item.name.indexOf(name) != -1 || item.dishTypeId == dishTypeId)
            }
        }
    })
}

// 套餐管理-获取餐次类型设置
const getCanteenMealSetTypeList = async () => {
    await Http.get('/cloud/canteenMealSetType/get').then(({ data }) => {
        state.modelOptions = data
        // 回显套餐类型
        data.forEach(item => {
            if (item.code == props.paramsEdit.mealSetTypeCode) {
                state.formState.mealSetTypeId = item.id
            }
        })
    })
}
// 菜品
const getDishList = async () => {
    const params = {
        ...state.dishForm,
        ...state.searchForm.form,
    }
    await Http.post('/cloud/canteenDish/page', params).then(({ data }) => {
        state.menuList =
            data.list?.map(item => {
                return {
                    ...item,
                    isShow: false,
                }
            }) || []
    })
}

// 获取菜品菜品类型
const canteenDishTypeList = async () => {
    await Http.post('/cloud/canteenDishType/list', {}).then(({ data }) => {
        state.dishTypeList = data || []
    })
}
// 选择复选框
const checkboxDishList = event => {
    state[state.isCheckboxRadio ? 'selectdDish' : 'selectdDishs'] = event
    state.searchForm.dishList = state.menuList.filter(item => event.includes(item.id))
}

// 删除菜单
const handlerDelete = id => {
    if (state.isCheckboxRadio) {
        state.selectdDish.splice(state.selectdDishs.indexOf(id), 1)
    } else {
        state.selectdDishs.splice(state.selectdDishs.indexOf(id), 1)
    }
    state.searchForm.dishList = state.searchForm.dishList.filter(item => item.id != id)
}

// 打开菜品菜单
const handlerDishOpen = async isCheckboxRadio => {
    state.editDishTypeOpen = true
    await getDishList()
    await canteenDishTypeList()
    state.isCheckboxRadio = isCheckboxRadio
    let ids = []
    // 这样搞用于回显菜单
    // 单选
    if (isCheckboxRadio) {
        ids = state.formState.dish.map(item => item.dishId)
        state.selectdDish = ids
    } else {
        // 多选
        ids = state.formState.dishs.map(item => item.dishId)
        state.selectdDishs = ids
    }
    state.searchForm.dishList = ids.length ? state.menuList.filter(item => ids.includes(item.id)) : []
}

// 取消
const handleCancel = () => {
    state.searchForm.form.dishTypeId = null
    state.searchForm.form.name = ''
    state.editDishTypeOpen = false
}
// 确定选择菜品：
const handleOk = () => {
    // 多选菜品: dishs, 单点菜品： dish
    let _dish = state.isCheckboxRadio ? 'dish' : 'dishs'
    let _dishName = []
    if (_dish === 'dishs') {
        if (state.searchForm.dishList.length) {
            state.formState.price = 0
        } else {
            state.formState.price = null
        }
    }
    state.formState[_dish] = state.searchForm.dishList.map(v => {
        _dishName.push(v.name)
        if (_dish === 'dishs') {
            state.formState.price += v.price
        }
        return {
            dishId: v.id,
            isSingleDish: state.isCheckboxRadio,
        }
    })
    if (_dish === 'dishs' && state.formState.price) {
        state.formState.price = state.formState.price.toFixed(2)
    }

    // 单选
    if (state.isCheckboxRadio) {
        state.formState.dishName = _dishName.join(',') || ''
    } else {
        // 多选
        state.formState.dishsName = _dishName.join(',') || ''
        addPackageRef.value.validateFields(['dishsName'])
    }
    handleCancel()
}

const handlePriceChange = value => {
    // 将输入的值转换为数字并保留两位小数
    const num = parseFloat(value)
    if (!isNaN(num)) {
        state.formState.price = num.toFixed(2)
    } else {
        state.formState.price = ''
    }
}
onMounted(async () => {
    props.paramsEdit?.id && initPage()
    await getCanteenMealSetTypeList()
})
</script>

<style lang="less" scoped>
.addPackage {
    position: relative;

    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;
        }
    }

    .pageBody {
        padding: 20px;
        width: 68%;
        height: calc(100vh - 160px);
        margin: 0 auto;
        overflow-y: auto;

        :deep(.ant-form-item) {
            margin-bottom: 20px;
        }
    }

    .footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        z-index: 999;
        border-top: 1px solid @border-color-base;
        background-color: @body-background;
        padding: 20px 0;

        .ant-btn {
            margin: 0 8px;
        }
    }
}

.select-pachage-content {
    display: flex;
    justify-content: space-between;
    height: 545px;

    .pending-pool {
        background-color: @gray-background;
        flex: 1;
        padding: 10px;

        .search-Form {
            padding: 0 10px;
        }

        .columns-group {
            height: 500px;
            overflow-y: auto;
            display: grid;
            /* 设置5列等宽布局 */
            grid-template-columns: repeat(5, 1fr);
            /* 设置网格间距 */
            width: 100%;

            .columns-group-checkbox {
                position: relative;
                margin: 12px 0;
                display: grid;
                display: flex;
                flex-direction: column;
                align-items: center;

                // text-align: center;
                :deep(.ant-radio),
                :deep(.ant-checkbox) {
                    position: absolute;
                    top: 0;
                    right: 19px;
                    z-index: 999;
                }

                &:after {
                    display: none;
                }
            }

            .title {
                width: 86px;
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .price {
                color: #999999ff;
            }
        }
    }

    .selected-pool {
        width: 200px;
        border-left: 1px solid @border-color-base;

        .selected-pool_title {
            padding: 8px 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .selected-dishes {
                font-size: 16px;
                font-weight: 500;

                &:before {
                    content: '';
                    margin-right: 6px;
                    display: inline-block;
                    vertical-align: middle;
                    width: 2px;
                    height: 14px;
                    background-color: @primary-color;
                }
            }

            .selected-num {
                font-size: 16px;
                font-weight: 500;
            }
        }

        .selected-pool_list {
            overflow-y: auto;
            height: 452px;

            .pool_list_item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 6px 10px;

                .title {
                    width: 100px;
                    display: block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .delete-icon {
                    cursor: pointer;
                    color: @error-color;
                }
            }
        }

        .footer {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px 0;
        }
    }
}
</style>
