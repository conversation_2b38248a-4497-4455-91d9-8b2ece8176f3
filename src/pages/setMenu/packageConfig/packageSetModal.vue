<template>
    <YModal width="750px" v-model:open="props.open" title="订餐设置" :bodyStyle="{ padding: '24px' }" @cancel="handleCancel">
        <a-form
            :model="state.packageSetForm"
            ref="packageSetRef"
            layout="vertical"
            :rules="packageSetRule"
            :colon="false"
            class="package-set-form"
        >
        {{state.packageSetForm}}
            <a-form-item class="schedule-item" label="订餐截止时间：" :required="true">
                <div class="schedule-item__controls">
                    <span class="schedule-text">提前</span>
                    <a-form-item name="orderDeadlineAdvanceDays" no-style>
                        <a-input-number
                            style="width: 80px"
                            :min="0"
                            :precision="0"
                            v-model:value="state.packageSetForm.orderDeadlineAdvanceDays"
                            placeholder="请输入"
                        />
                    </a-form-item>
                    <span class="schedule-text">天</span>
                    <a-form-item name="orderDeadlineTime" no-style>
                        <a-time-picker
                            style="width: 110px"
                            v-model:value="state.packageSetForm.orderDeadlineTime"
                            format="HH:mm"
                            valueFormat="HH:mm"
                            placeholder="请选择"
                        />
                    </a-form-item>
                    <a-form-item name="orderDeadlineEnable" no-style>
                        <a-switch
                            v-model:checked="state.packageSetForm.orderDeadlineEnable"
                            checked-children="开"
                            un-checked-children="关"
                        />
                    </a-form-item>
                </div>
            </a-form-item>

            <a-form-item class="schedule-item" label="取消订餐时间：" :required="true">
                <div class="schedule-item__controls">
                    <span class="schedule-text">提前</span>
                    <a-form-item name="cancelDeadlineAdvanceDays" no-style>
                        <a-input-number
                            style="width: 80px"
                            :min="0"
                            :precision="0"
                            v-model:value="state.packageSetForm.cancelDeadlineAdvanceDays"
                            placeholder="请输入"
                        />
                    </a-form-item>
                    <span class="schedule-text">天</span>
                    <a-form-item name="cancelDeadlineTime" no-style>
                        <a-time-picker
                            style="width: 110px"
                            v-model:value="state.packageSetForm.cancelDeadlineTime"
                            format="HH:mm"
                            valueFormat="HH:mm"
                            placeholder="请选择"
                        />
                    </a-form-item>
                    <a-form-item name="cancelDeadlineEnable" no-style>
                        <a-switch
                            v-model:checked="state.packageSetForm.cancelDeadlineEnable"
                            checked-children="开"
                            un-checked-children="关"
                        />
                    </a-form-item>
                </div>
            </a-form-item>

            <a-form-item class="schedule-item" label="默认套餐生成时间：" :required="true">
                <div class="schedule-item__controls">
                    <span class="schedule-text">提前</span>
                    <a-form-item name="defaultPackageAdvanceDays" no-style>
                        <a-input-number
                            style="width: 80px"
                            :min="0"
                            :precision="0"
                            v-model:value="state.packageSetForm.defaultPackageAdvanceDays"
                            placeholder="请输入"
                        />
                    </a-form-item>
                    <span class="schedule-text">天</span>
                    <a-form-item name="defaultPackageTime" no-style>
                        <a-time-picker
                            style="width: 110px"
                            v-model:value="state.packageSetForm.defaultPackageTime"
                            format="HH:mm"
                            valueFormat="HH:mm"
                            placeholder="请选择"
                        />
                    </a-form-item>
                    <span class="schedule-text">生成未来</span>
                    <a-form-item name="defaultPackageFutureDays" no-style>
                        <a-input-number
                            style="width: 80px"
                            :min="1"
                            :precision="0"
                            v-model:value="state.packageSetForm.defaultPackageFutureDays"
                            placeholder="请输入"
                        />
                    </a-form-item>
                    <span class="schedule-text">天的默认套餐</span>
                    <a-form-item name="defaultPackageEnable" no-style>
                        <a-switch
                            v-model:checked="state.packageSetForm.defaultPackageEnable"
                            checked-children="开"
                            un-checked-children="关"
                        />
                    </a-form-item>
                </div>
            </a-form-item>
        </a-form>
        <div class="tips">
            <p>说明：</p>
            <p>1.如就餐日期为2025.01.12，那么2025.01.11 17:00就不能再预定套餐了；</p>
            <p>
                2.开启默认套餐后，学生未点餐会根据上方设定时间进行生成默认套餐，
                <span style="color: red">若更改时间则会第二天根据上方设定的时间点生成默认套餐。</span>
            </p>
        </div>

        <template #footer>
            <a-button key="back" @click="handleCancel">取消</a-button>
            <a-button key="submit" type="primary" @click="handlePackageSet">确定</a-button>
        </template>
    </YModal>
</template>

<script setup>
import { reactive, watch, shallowRef } from 'vue'
import Http from '@/utils/http.js'

// 套餐设置表单校验规则，确保三个时间节点与天数均完整填写
const packageSetRule = {
    orderDeadlineAdvanceDays: [{ required: true, type: 'number', message: '请输入订餐截止时间的提前天数' }],
    orderDeadlineTime: [{ required: true, message: '请选择订餐截止具体时间' }],
    cancelDeadlineAdvanceDays: [{ type: 'number', required: true, message: '请输入取消时间的提前天数' }],
    cancelDeadlineTime: [{ required: true, message: '请选择取消订餐时间' }],
    defaultPackageAdvanceDays: [{ required: true, type: 'number', message: '请输入默认套餐生成的提前天数' }],
    defaultPackageTime: [{ required: true, message: '请选择默认套餐生成时间' }],
    defaultPackageFutureDays: [{ required: true, type: 'number', message: '请输入默认套餐生成的天数' }],
}
const packageSetRef = shallowRef()

/**
 * 创建套餐设置表单的默认结构
 * @returns {Object} 完整的套餐设置初始数据
 */
const createDefaultForm = () => ({
    id: '',
    orderDeadlineAdvanceDays: 0,
    orderDeadlineTime: '17:00',
    orderDeadlineEnable: false,
    cancelDeadlineAdvanceDays: 1,
    cancelDeadlineTime: '17:00',
    cancelDeadlineEnable: false,
    defaultPackageAdvanceDays: 1,
    defaultPackageTime: '00:00',
    defaultPackageFutureDays: 7,
    defaultPackageEnable: false,
})

const props = defineProps({
    open: {
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['update:open', 'emitGetPackageSet'])
// 组件本地状态，包含套餐设置表单实体
const state = reactive({
    packageSetForm: createDefaultForm(),
})
/**
 * 获取套餐设置并回填表单
 */
const handlegetPackageSet = () => {
    Http.get('/cloud/canteenOrderSetting/get').then(({ data }) => {
        Object.assign(state.packageSetForm, createDefaultForm(), data || {})
    })
}
/**
 * 关闭套餐设置弹窗，重置表单并通知父组件
 */
const handleCancel = () => {
    packageSetRef.value.resetFields()
    emit('update:open', false)
}
/**
 * 提交套餐设置表单
 */
const handlePackageSet = () => {
    packageSetRef.value.validate().then(() => {
        Http.post('/cloud/canteenOrderSetting/update', state.packageSetForm).then(({ message }) => {
            YMessage.success(message)
            handleCancel()
        })
    })
}

watch(
    () => props.open,
    val => {
        val && handlegetPackageSet()
    },
)
</script>

<style lang="less" scoped>
.package-set-form {
    width: 100%;

    :deep(.ant-form-item) {
        margin-bottom: 16px;
    }
}

.schedule-item {
    :deep(.ant-form-item-label) {
        min-width: 130px;
    }

    :deep(.ant-form-item-control-input-content) {
        width: 100%;
    }
}

.schedule-item__controls {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.schedule-text {
    color: #333;
    font-size: 14px;
}

.tips {
    color: @primary-color;
    margin-top: 20px;
}
</style>
