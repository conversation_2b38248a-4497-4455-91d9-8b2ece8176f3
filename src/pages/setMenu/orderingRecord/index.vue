<template>
    <div class="package-statistics">
        <div class="pageHead">
            <div class="pageHead_title">
                <a-tabs v-model:activeKey="activeKey" @change="handlerActiveKey">
                    <a-tab-pane v-for="item in mealSetTypes" :key="item.id" :tab="item.name"></a-tab-pane>
                </a-tabs>
            </div>
        </div>
        <div class="pageBody">
            <div class="ordering_record">
                <searchForm class="search_form" v-model:formState="form" :formList="formList" @submit="submit" @reset="reset">
                    <template #startEndRefundTime>
                        <a-range-picker
                            v-model:value="form.startEndRefundTime"
                            format="YYYY-MM-DD"
                            :allowClear="false"
                            valueFormat="YYYY-MM-DD"
                            @calendarChange="onCalendarChange"
                            :presets="servingTimePresets"
                        />
                    </template>
                </searchForm>
                <div class="btn_group">
                    <a-button @click="handleExport" :loading="state.exportLoading">导出</a-button>
                </div>
                <ETable
                    :columns="columns"
                    :minH="450"
                    :data-source="state.dataSource"
                    :paginations="state.pagination"
                    :loading="state.tabLoading"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                        <template v-if="column.dataIndex == 'operate'">
                            <a-button type="link" class="btn-link-color" @click="handleDetails(record)">详情</a-button>
                        </template>
                    </template>
                </ETable>
            </div>
        </div>
        <YModal v-model:open="state.mealDetailsModel" title="套餐详情" width="860px" :footer="null" @cancel="handleCancel">
            <div style="padding: 24px">
                <a-row :gutter="[16, 18]">
                    <YCol :span="8">
                        {{ selectItem.userName || '-' }}
                    </YCol>
                    <YCol :span="8">
                        {{ selectItem.orgName || '-' }}
                    </YCol>
                    <YCol :span="8"></YCol>
                    <YCol :span="8">套餐类型：{{ mealDetails.mealSetTypeName || '-' }}</YCol>
                    <YCol :span="8">套餐名称：{{ mealDetails.mealSetVersionName || '-' }}</YCol>
                    <YCol :span="8">套餐价格：{{ mealDetails.totalPrice || '-' }}</YCol>
                    <YCol :span="24">
                        <div class="meal_details_content">
                            <span>菜品名称：</span>
                            <div class="food_list" v-if="mealDetails.dishList && mealDetails.dishList.length">
                                <a-row :gutter="[16, 18]" style="width: 100%">
                                    <YCol :span="8" v-for="item in mealDetails.dishList" :key="item.id">
                                        <div class="food_item">
                                            <img class="food_img" :src="item.dishLogo" alt="" />
                                            <span class="name">{{ item.dishName }}</span>
                                            <span class="dish_price">¥{{ item.dishPrice }}</span>
                                        </div>
                                    </YCol>
                                </a-row>
                            </div>
                            <div v-else>-</div>
                        </div>
                    </YCol>
                    <YCol :span="24">
                        <div class="meal_details_content">
                            <span>额外菜品：</span>
                            <div class="food_list" v-if="mealDetails.singleDishList && mealDetails.singleDishList.length">
                                <a-row :gutter="[16, 18]" style="width: 100%">
                                    <YCol :span="8" v-for="item in mealDetails.singleDishList" :key="item.id">
                                        <div class="food_item">
                                            <img class="food_img" :src="item.dishLogo" alt="" />
                                            <span class="name">{{ item.dishName }}</span>
                                            <span class="dish_price">¥{{ item.dishPrice }}</span>
                                        </div>
                                    </YCol>
                                </a-row>
                            </div>
                            <div v-else>-</div>
                        </div>
                    </YCol>
                    <YCol :span="8">点餐时间：{{ selectItem.createTime || '-' }}</YCol>
                    <YCol :span="8">就餐状态：{{ selectItem.servingStatusName || '-' }}</YCol>
                    <YCol :span="8">就餐时间：{{ selectItem.servingTime || '-' }}</YCol>
                </a-row>
            </div>
        </YModal>
    </div>
</template>

<script setup>
import dayjs from 'dayjs'
import Http from '@/utils/http.js'

const activeKey = ref('student')

const mealSetTypes = [
    {
        id: 'student',
        name: '学生',
    },
    {
        id: 'teacher',
        name: '老师',
    },
]

const mealDetails = ref({})
const selectItem = ref({})
const state = reactive({
    mealDetailsModel: false,
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    tabLoading: false,
    exportLoading: false,
    dataSource: [],
    sort: {
        orderField: '',
        orderSort: '',
    },
})

const createTime = ref([])
const form = ref({})
const mealSetTypeList = ref([])

const columns = computed(() => {
    return [
        { title: '序号', dataIndex: 'index', width: 80 },
        { title: '姓名', dataIndex: 'userName', key: 'userName' },
        { title: activeKey.value == 'student' ? '班级' : '部门', dataIndex: 'orgName', key: 'orgName' },
        { title: '套餐类型', dataIndex: 'mealSetTypeName', key: 'mealSetTypeName' },
        { title: '套餐名称', dataIndex: 'mealSetVersionName', key: 'mealSetVersionName' },
        { title: '价格', dataIndex: 'totalPrice', key: 'totalPrice' },
        { title: '点餐模式', dataIndex: 'orderModeName', key: 'orderModeName' },
        { title: '预定日期', dataIndex: 'orderDate', key: 'orderDate' },
        { title: '点餐时间', dataIndex: 'createTime', key: 'createTime', sorter: true },
        { title: '就餐状态', dataIndex: 'servingStatusName', key: 'servingStatusName' },
        { title: '就餐时间', dataIndex: 'servingTime', key: 'servingTime', sorter: true },
        { title: '备注', dataIndex: 'remark', key: 'remark' },
        { title: '操作', dataIndex: 'operate', key: 'operate' },
    ]
})

/**
 * 就餐时间快捷预设，提供“最近一周 / 最近一个月 / 最近三个月”快速筛选
 */
const servingTimePresets = computed(() => {
    const now = dayjs()
    const weekStart = dayjs().weekday(1).startOf('day')
    const weekEnd = dayjs().weekday(7).endOf('day')
    const monthStart = now.startOf('month').startOf('day')
    const monthEnd = now.endOf('month').endOf('day')
    const threeMonthsStart = now.subtract(2, 'month').startOf('month').startOf('day')
    return [
        { label: '最近一周', value: [weekStart, weekEnd] },
        { label: '最近一个月', value: [monthStart, monthEnd] },
        { label: '最近三个月', value: [threeMonthsStart, monthEnd] },
    ]
})

const formList = computed(() => {
    return [
        {
            type: 'input',
            value: 'userName',
            label: '姓名',
        },
        {
            type: 'input',
            value: 'orgName',
            label: activeKey.value == 'student' ? '班级' : '部门',
        },

        {
            type: 'select',
            value: 'mealSetTypeId',
            label: '套餐类型',
            list: mealSetTypeList.value,
        },
        {
            type: 'slot',
            label: '预定日期',
            span: 7,
            value: 'startEndRefundTime',
            attrs: {
                valueFormat: 'YYYY-MM-DD',
                format: 'YYYY-MM-DD',
            },
        },
        {
            type: 'select',
            value: 'servingStatus',
            label: '就餐状态',
            list: [
                { label: '全部', value: null },
                { label: '未就餐', value: 0 },
                { label: '已就餐', value: 1 },
                { label: '已取消（请假）', value: 2 },
            ],
        },
        {
            type: 'rangePicker',
            label: '就餐时间',
            value: ['servingStartTime', 'servingEndTime'],
            attrs: {
                presets: servingTimePresets.value,
            },
        },
    ]
})
const submit = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getList()
}

const getDishes = computed(() => {
    return type => {
        return mealDetails.value?.mealSetDishes?.filter(i => (type == 'meal' ? !i.isSingleDish : i.isSingleDish))
    }
})

function handleCancel() {
    state.mealDetailsModel = false
}

const reset = () => {
    form.value = {}
    initTime()
    submit()
}

const handlerActiveKey = key => {
    activeKey.value = key
    reset()
}

function getMealSetType() {
    Http.get('/cloud/canteenMealSetType/get').then(res => {
        const arr = res.data?.map(i => {
            return {
                label: i.name,
                value: i.id,
            }
        })
        mealSetTypeList.value = [{ label: '全部', value: null }].concat(arr)
    })
}

// 分页
const handleTableChange = ({ current, pageSize, total }, filters, { field, order }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    state.sort.field = ''
    state.sort.order = ''
    if (order) {
        state.sort.orderField = field == 'createTime' ? 'create_time' : 'serving_time'
        state.sort.orderSort = order == 'ascend' ? 'asc' : 'desc'
    }
    getList()
}

function getList() {
    state.tabLoading = true
    const params = {
        ...state.pagination,
        ...state.sort,
        ...form.value,
        userType: activeKey.value == 'student' ? 0 : 1,
    }
    Http.post('/cloud/canteenStudentOrder/selectOrderRecordPage', params)
        .then(({ data }) => {
            state.dataSource = data.list
            state.pagination.pageNo = data.pageNo || 1
            state.pagination.pageSize = data.pageSize || 10
            state.pagination.total = data.total
        })
        .finally(() => {
            state.tabLoading = false
        })
}

const onCalendarChange = val => {
    form.value.startEndRefundTime = val
    form.value.startTime = val ? val[0] : ''
    form.value.endTime = val ? val[1] : ''
}

function handleDetails(item) {
    state.mealDetailsModel = true
    selectItem.value = item
    Http.get('/cloud/canteenStudentOrder/get', { id: item.id }).then(res => {
        mealDetails.value = res.data
    })
    console.log(mealDetails.value, 'itemitemitemitem')
}

const handleExport = async () => {
    try {
        state.exportLoading = true
        const title = `${activeKey.value == 'student' ? '学生' : '老师'}点餐记录.xlsx`
        Http.download(
            '/cloud/canteenStudentOrder/orderRecordExport',
            'post',
            {
                ...form.value,
                ...state.sort,
                userType: activeKey.value == 'student' ? 0 : 1,
            },
            title,
        )
    } catch (error) {
        YMessage.error('导出失败：' + error.message)
    } finally {
        state.exportLoading = false
    }
}

const initTime = () => {
    const now = dayjs()
    const [start, end] = [now.subtract(6, 'month').format('YYYY-MM-DD'), now.add(6, 'month').format('YYYY-MM-DD')]
    form.value.startEndRefundTime = [start, end]
    form.value.startTime = start
    form.value.endTime = end
}

onMounted(() => {
    getMealSetType()
    initTime()
    getList()
})
</script>

<style lang="less" scoped>
.package-statistics {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            text-align: left;
            font-style: normal;
            flex: 1;
            :deep(.ant-tabs-nav) {
                margin: 0;
                .ant-tabs-nav-wrap {
                    height: 62px;
                }
            }
        }
    }

    .pageBody {
        padding: 20px;
    }
}
.ordering_record {
    .search_form {
        margin: 20px 0;
    }
}
.btn_group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 18px 0;
}
.meal_details_content {
    display: flex;
    flex-wrap: wrap;
    .food_list {
        display: flex;
        flex-wrap: wrap;
        flex: 1;
    }
}
.food_item {
    margin-right: 20px;
    display: flex;
    flex-direction: column;
    .food_img {
        width: 86px;
        height: 86px;
        border-radius: 4px;
    }
    .name {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin: 8px 0px 4px 0px;
    }
    .dish_price {
        font-weight: 400;
        font-size: 14px;
        color: #999999;
        line-height: 20px;
    }
}
</style>
