<template>
    <div class="package-statistics">
        <div class="pageBody">
            <a-radio-group v-model:value="state.orderType" button-style="solid">
                <a-radio-button value="timeDimension">时间维度</a-radio-button>
                <a-radio-button value="classDimension" v-auth="tradeOrder">班级维度</a-radio-button>
                <a-radio-button value="packageDimension" v-auth="refundOrder">套餐维度</a-radio-button>
            </a-radio-group>
            <component :is="state.components"></component>
            <Empty v-if="state.isEmpty" tips="暂无权限" />
        </div>
    </div>
</template>

<script setup>
import { onMounted, reactive, watch, shallowRef } from 'vue'
const store = useStore()
const tradeOrder = 'setMenu.packageStatistics.studentDimension.classDimension'
const refundOrder = 'setMenu.packageStatistics.studentDimension.packageDimension'
const TimeDimension = defineAsyncComponent(() => import('./timeDimension.vue'))
const ClassDimension = defineAsyncComponent(() => import('./classDimension.vue'))
const PackageDimension = defineAsyncComponent(() => import('./packageDimension.vue'))

const state = reactive({
    isEmpty: false,
    orderType: 'timeDimension',
    components: shallowRef(null),
})

watch(
    () => state.orderType,
    val => {
        const comList = {
            classDimension: shallowRef(ClassDimension),
            timeDimension: shallowRef(TimeDimension),
            packageDimension: shallowRef(PackageDimension),
        }
        state.components = comList[val]
    },
    {
        deep: true,
        immediate: true,
    },
)
onMounted(() => {
    //按钮权限控制判断。 如果订餐交易订单存在， 则赋值，并不再向下执行
    // const _tradeOrder = store.Perms.some(v => v == tradeOrder)
    // if (_tradeOrder) {
    //     state.isEmpty = false
    //     state.orderType = 'classDimension'
    //     // state.components = shallowRef(ClassDimension)
    // } else {
    //     // 如果订餐退费订单存在， 则赋值，并不再向下执行
    //     const _refundOrder = store.Perms.some(v => v == refundOrder)
    //     if (_refundOrder) {
    //         state.isEmpty = false
    //         state.orderType = 'packageDimension'
    //         // state.components = shallowRef(PackageDimension)
    //     } else {
    //         state.isEmpty = true
    //     }
    // }
    state.orderType = 'timeDimension'
    state.isEmpty = false
})
</script>

<style lang="less" scoped>
.package-statistics {
    // .pageHead {
    //     display: flex;
    //     align-items: center;
    //     padding: 0 20px;
    //     height: 62px;
    //     border-bottom: 1px solid @border-color-base;

    //     .pageHead_title {
    //         font-weight: 600;
    //         font-size: 18px;
    //         line-height: 25px;
    //         text-align: left;
    //         font-style: normal;
    //     }
    // }

    // .pageBody {
    //     // padding: 20px;
    // }
}
</style>
