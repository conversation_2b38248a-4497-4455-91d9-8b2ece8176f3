<template>
    <div class="class-dimension">
        <searchForm
            class="reset-search-form"
            v-model:formState="state.form"
            :formList="formList"
            @submit="queryInitData"
            @reset="queryInitData"
        >
            <template #startEndRefundTime>
                <a-range-picker
                    v-model:value="state.form.startEndRefundTime"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    @calendarChange="onCalendarChange"
                />
            </template>
        </searchForm>
        <div style="text-align: right">
            <a-button v-auth="'setMenu.packageStatistics.studentDimension.classExport'" @click="handleExport">导出</a-button>
        </div>
        <a-tabs v-model:activeKey="state.activeKey" @change="handlerActiveKey">
            <a-tab-pane v-for="item in state.mealSetTypes" :key="item.id" :tab="item.name"></a-tab-pane>
        </a-tabs>
        <a-spin :spinning="state.spinning">
            <a-descriptions class="reset-descriptions" v-for="item in state.dataSource" :title="item.orgName">
                <a-descriptions-item label="套餐数">
                    <span v-for="it in item.orderMealList">{{ it.mealSetVersionName }}-{{ it.orderMealSetNum }}</span>
                </a-descriptions-item>
            </a-descriptions>
            <Empty v-if="state.dataSource.length === 0" />
        </a-spin>

        <Pagination
            style="text-align: right"
            :hideOnSinglePage="state.pagination.total <= 10"
            :total="state.pagination.total"
            :current="state.pagination.pageNo"
            :pageSize="state.pagination.pageSize"
            @paginationChange="handleTableChange"
        />
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import Http from '@/utils/http.js'
import dayjs from 'dayjs'

const formList = [
    {
        type: 'input',
        value: 'orgName',
        label: '班级名称',
    },
    {
        type: 'slot',
        value: 'startEndRefundTime',
        label: '点餐时间',
        span: 7,
        attrs: {
            valueFormat: 'YYYY-MM-DD',
            format: 'YYYY-MM-DD',
        },
    },
]
const state = reactive({
    loading: false,
    spinning: false,
    form: {
        mealSetTypeId: '',
        orgName: '',
        startEndRefundTime: null,
        startTime: '',
        endTime: '',
    },
    activeKey: '',
    activeTitle: '',
    mealSetTypes: [],
    dataSource: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
})

// 添加导出功能
const handleExport = async () => {
    try {
        state.loading = true
        const params = {
            ...state.form,
            mealSetTypeId: state.activeKey,
        }
        Http.download(
            '/cloud/canteenStudentOrder/groupClassesExport',
            'post',
            params,
            `班级-智慧点餐-${state.activeTitle}-套餐统计${params.startTime}-${params.endTime}.xlsx`,
        )
    } catch (error) {
        YMessage.error('导出失败：' + error.message)
    } finally {
        state.loading = false
    }
}
const initPage = async () => {
    const params = {}
    await Http.get('/cloud/canteenMealSetType/get', params).then(({ data }) => {
        state.mealSetTypes = data
        state.activeKey = data[0]?.id || ''
        state.activeTitle = data[0]?.name || ''
    })
}
// 套餐列表
const getInitData = async () => {
    const params = {
        ...state.form,
        ...state.pagination,
        mealSetTypeId: state.activeKey,
    }
    state.spinning = true
    await Http.post('/cloud/canteenStudentOrder/selectGroupClassesPage', params)
        .then(({ data }) => {
            const { pageNo, pageSize, total, list } = data
            state.dataSource = list
            state.pagination.pageNo = pageNo
            state.pagination.pageSize = pageSize
            state.pagination.total = total
        })
        .finally(() => {
            state.spinning = false
        })
}
// 分页
const handleTableChange = item => {
    const { pageNo, pageSize, total } = item
    state.pagination.pageNo = pageNo
    state.pagination.pageSize = pageSize
    state.pagination.total = total || 0
    getInitData()
}
const initTime = () => {
    const now = dayjs()
    const [start, end] = [now.subtract(6, 'month').format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
    state.form.startEndRefundTime = [start, end]
    state.form.startTime = start
    state.form.endTime = end
}

// 查询 重置
const queryInitData = item => {
    !item && initTime()
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getInitData()
}
const handlerActiveKey = item => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    state.pagination.total = 0
    state.activeTitle = state.mealSetTypes.find(it => it.id === item)?.name || ''
    getInitData()
}
const disabledDate = current => {
    if (!state.form.startEndRefundTime || state.form.startEndRefundTime.length === 0) {
        return false
    }
    // 确保使用dayjs对象进行比较
    const startDate = state.form.startEndRefundTime[0] ? dayjs(state.form.startEndRefundTime[0]) : null
    const endDate = state.form.startEndRefundTime[1] ? dayjs(state.form.startEndRefundTime[1]) : null

    // 限制选择范围在6个月内
    const tooLate = startDate && current.diff(startDate, 'months') > 5
    const tooEarly = endDate && endDate.diff(current, 'months') > 5
    return tooEarly || tooLate
}

const onCalendarChange = val => {
    state.form.startEndRefundTime = val
    state.form.startTime = val ? val[0] : ''
    state.form.endTime = val ? val[1] : ''
}

onMounted(async () => {
    initTime()
    await initPage()
    await getInitData()
})
</script>
<style lang="less" scoped>
.reset-search-form {
    margin: 20px 0;
}

.reset-descriptions {
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-bottom: 20px;

    :deep(.ant-descriptions-header) {
        background: #f5f5f5;
        padding: 10px 20px;
        margin: 0;
    }

    :deep(.ant-descriptions-item) {
        padding: 10px 20px;

        .ant-descriptions-item-label {
            font-weight: 600;
            width: 100px;
        }

        .ant-descriptions-item-content {
            display: flex;
            justify-content: space-between;
        }
    }
}
</style>
