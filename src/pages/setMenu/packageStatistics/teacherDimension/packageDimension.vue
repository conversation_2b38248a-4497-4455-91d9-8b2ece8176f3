<template>
    <div class="package-dimension">
        <searchForm
            class="reset-search-form"
            v-model:formState="state.form"
            :formList="formList"
            @submit="queryInitData"
            @reset="queryInitData"
        >
            <template #startEndRefundTime>
                <a-range-picker
                    v-model:value="state.form.startEndRefundTime"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    @calendarChange="onCalendarChange"
                />
            </template>
        </searchForm>

        <div style="text-align: right">
            <a-button v-auth="'setMenu.packageStatistics.teacherDimension.packageExport'" @click="handleExport">导出</a-button>
        </div>
        <a-tabs v-model:activeKey="state.activeKey" @change="handlerActiveKey">
            <a-tab-pane v-for="item in state.mealSetTypes" :key="item.id" :tab="item.name"></a-tab-pane>
        </a-tabs>
        <ETable
            :columns="columns"
            :data-source="state.dataSource"
            :paginations="state.pagination"
            :loading="state.loading"
            @change="handleTableChange"
        >
            <template #bodyCell="{ column, text, record, index }">
                <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip>
            </template>
        </ETable>
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import Http from '@/utils/http.js'
import dayjs from 'dayjs'

const columns = [
    { title: '序号', dataIndex: 'index' },
    { title: '套餐名称', dataIndex: 'mealSetVersionName' },
    { title: '套餐数', dataIndex: 'orderMealSetNum' },
]
const formList = shallowRef([
    {
        type: 'input',
        value: 'mealSetVersionName',
        label: '套餐名称',
    },
    {
        type: 'slot',
        value: 'startEndRefundTime',
        label: '点餐时间',
        span: 7,
        attrs: {
            valueFormat: 'YYYY-MM-DD',
            format: 'YYYY-MM-DD',
        },
    },
])

const state = reactive({
    loading: false,
    form: {
        mealSetVersionName: '',
        startEndRefundTime: null,
        startTime: '',
        endTime: '',
    },
    activeKey: '',
    activeTitle: '',
    mealSetTypes: [],
    dataSource: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
})
// 添加导出功能
const handleExport = async () => {
    try {
        state.loading = true
        const params = {
            ...state.form,
            mealSetTypeId: state.activeKey,
            userType: 1,
        }
        Http.download(
            '/cloud/canteenStudentOrder/groupMealSetExport',
            'post',
            params,
            `部门-智慧点餐-${state.activeTitle}-套餐统计${params.startTime}-${params.endTime}.xlsx`,
        )
    } catch (error) {
        YMessage.error('导出失败：' + error.message)
    } finally {
        state.loading = false
    }
}
const getInitData = () => {
    state.loading = true
    const params = {
        ...state.form,
        ...state.pagination,
        mealSetTypeId: state.activeKey,
        userType: 1,
    }

    Http.post('/cloud/canteenStudentOrder/selectGroupMealSetPage', params)
        .then(({ data }) => {
            const { pageNo, pageSize, total, list } = data
            state.dataSource = list
            state.pagination.pageNo = pageNo
            state.pagination.pageSize = pageSize
            state.pagination.total = total
        })
        .finally(() => {
            state.loading = false
        })
}
// 分页
const handleTableChange = ({ current, pageSize, total }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    getInitData()
}

const initPage = async () => {
    const params = {}
    await Http.get('/cloud/canteenMealSetType/get', params).then(({ data }) => {
        state.mealSetTypes = data
        state.activeKey = data[0]?.id || ''
        state.activeTitle = data[0]?.name || ''
    })
}

const handlerActiveKey = item => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    state.pagination.total = 0
    state.activeTitle = state.mealSetTypes.find(it => it.id === item)?.name || ''
    getInitData()
}
const initTime = () => {
    const now = dayjs()
    const [start, end] = [now.subtract(6, 'month').format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
    state.form.startEndRefundTime = [start, end]
    state.form.startTime = start
    state.form.endTime = end
}

// 查询 重置
const queryInitData = item => {
    !item && initTime()
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getInitData()
}

const disabledDate = current => {
    if (!state.form.startEndRefundTime || state.form.startEndRefundTime.length === 0) {
        return false
    }
    // 确保使用dayjs对象进行比较
    const startDate = state.form.startEndRefundTime[0] ? dayjs(state.form.startEndRefundTime[0]) : null
    const endDate = state.form.startEndRefundTime[1] ? dayjs(state.form.startEndRefundTime[1]) : null

    // 限制选择范围在6个月内
    const tooLate = startDate && current.diff(startDate, 'months') > 5
    const tooEarly = endDate && endDate.diff(current, 'months') > 5
    return tooEarly || tooLate
}

const onCalendarChange = val => {
    state.form.startEndRefundTime = val
    state.form.startTime = val ? val[0] : ''
    state.form.endTime = val ? val[1] : ''
}

onMounted(async () => {
    initTime()
    await initPage()
    await getInitData()
})
</script>

<style lang="less" scoped>
.reset-search-form {
    margin: 20px 0;
}
</style>
