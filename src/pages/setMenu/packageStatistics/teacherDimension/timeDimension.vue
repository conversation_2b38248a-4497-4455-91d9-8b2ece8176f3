<template>
    <div class="time_dimension">
        <searchForm class="reset-search-form" v-model:formState="state.form" :formList="formList" @submit="submit" @reset="reset">
            <template #startEndRefundTime>
                <a-range-picker
                    v-model:value="state.form.startEndRefundTime"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    @calendarChange="onCalendarChange"
                />
            </template>
        </searchForm>
        <div class="btn_group">
            <a-button @click="handleExport" :loading="state.exportLoading">导出</a-button>
        </div>
        <ETable
            :columns="columns"
            :minH="450"
            :data-source="state.dataSource"
            :paginations="state.pagination"
            :loading="state.loading"
            @change="handleTableChange"
        >
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex == 'lunOrderMealList'">
                    {{ mealText(record.lunOrderMealList) }}
                </template>
                <template v-if="column.dataIndex == 'dinOrderMealList'">
                    {{ mealText(record.dinOrderMealList) }}
                </template>
                <template v-if="column.dataIndex == 'snkOrderMealList'">
                    {{ mealText(record.snkOrderMealList) }}
                </template>
                <template v-if="column.dataIndex == 'brkOrderMealList'">
                    {{ mealText(record.brkOrderMealList) }}
                </template>
            </template>
        </ETable>
    </div>
</template>

<script setup>
import dayjs from 'dayjs'
import Http from '@/utils/http.js'
import { computed, onMounted } from 'vue'
const columns = [
    { title: '点餐日期', dataIndex: 'orderDate', key: 'orderDate' },
    { title: '部门', dataIndex: 'orgName', key: 'orgName' },
    { title: '早餐套餐总数', dataIndex: 'brkOrderMealList', key: 'brkOrderMealList' },
    { title: '中餐套餐总数', dataIndex: 'lunOrderMealList', key: 'lunOrderMealList' },
    { title: '点心套餐总数', dataIndex: 'snkOrderMealList', key: 'snkOrderMealList' },
    { title: '晚餐套餐总数', dataIndex: 'dinOrderMealList', key: 'dinOrderMealList' },
]

const state = reactive({
    form: { startEndRefundTime: null, startTime: '', endTime: '' },
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    loading: false,
    exportLoading: false,
    dataSource: [],
})
const formList = [
    {
        type: 'input',
        value: 'orgName',
        label: '部门名称',
    },
    {
        type: 'slot',
        value: 'startEndRefundTime',
        label: '点餐时间',
        span: 7,
        attrs: {
            valueFormat: 'YYYY-MM-DD',
            format: 'YYYY-MM-DD',
        },
    },
]

const initTime = () => {
    const now = dayjs()
    const [start, end] = [now.subtract(6, 'month').format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]
    state.form.startEndRefundTime = [start, end]
    state.form.startTime = start
    state.form.endTime = end
}

const mealText = computed(() => {
    return mealList => {
        if (mealList && mealList.length) {
            const mealListText = mealList.map(i => `${i.mealSetVersionName}-${i.orderMealSetNum}`)
            return mealListText?.join('；') || '-'
        } else {
            return '-'
        }
    }
})

function getList() {
    state.loading = true
    const params = {
        ...state.pagination,
        ...state.form,
    }
    Http.post('/cloud/canteenStudentOrder/selectGroupDeptDatePage', params)
        .then(({ data }) => {
            state.dataSource = data.list
            state.pagination.pageNo = data.pageNo || 1
            state.pagination.pageSize = data.pageSize || 10
            state.pagination.total = data.total
        })
        .finally(() => {
            state.loading = false
        })
}

const onCalendarChange = val => {
    state.form.startEndRefundTime = val
    state.form.startTime = val ? val[0] : ''
    state.form.endTime = val ? val[1] : ''
}

// 查询
const submit = item => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getList()
}

// 重置
const reset = () => {
    state.form = {}
    initTime()
    submit()
}

// 分页
const handleTableChange = ({ current, pageSize, total }, filters, { field, order }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    getList()
}

const handleExport = async () => {
    try {
        state.exportLoading = true
        Http.download('/cloud/canteenStudentOrder/groupDeptDateExport', 'post', state.form, '时间部门套餐统计.xlsx')
    } catch (error) {
        YMessage.error('导出失败：' + error.message)
    } finally {
        state.exportLoading = false
    }
}

onMounted(() => {
    initTime()
    getList()
})
</script>

<style lang="less" scoped>
.time_dimension {
    .reset-search-form {
        margin: 20px 0;
    }
    .btn_group {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin: 18px 0;
    }
}
</style>
