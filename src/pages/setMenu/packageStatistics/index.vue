<template>
    <div class="package-statistics">
        <div class="pageHead">
            <div class="pageHead_title">
                <a-tabs v-model:activeKey="state.activeKey" @change="handlerActiveKey">
                    <a-tab-pane v-for="item in mealSetTypes" :key="item.id" :tab="item.name"></a-tab-pane>
                </a-tabs>
            </div>
        </div>
        <div class="pageBody">
            <component :is="state.components"></component>
        </div>
    </div>
</template>

<script setup>
import { onMounted, reactive, watch } from 'vue'
const store = useStore()
const tradeOrder = 'setMenu.packageStatistics.classDimension'
const refundOrder = 'setMenu.packageStatistics.packageDimension'
const StudentDimension = defineAsyncComponent(() => import('./studentDimension/index.vue'))
const TeacherDimension = defineAsyncComponent(() => import('./teacherDimension/index.vue'))
const state = reactive({
    isEmpty: false,
    orderType: 'studentDimension',
    components: shallowRef(StudentDimension),
    activeKey: 'studentDimension',
})
const mealSetTypes = [
    {
        id: 'studentDimension',
        name: '学生',
    },
    {
        id: 'teacher',
        name: '老师',
    },
]
const handlerActiveKey = () => {
    state.components = state.activeKey == 'studentDimension' ? shallowRef(StudentDimension) : shallowRef(TeacherDimension)
}
// watch(
//     () => state.orderType,
//     val => {
//         state.components = val == 'classDimension' ? classDimension : packageDimension
//     },
// )
onMounted(() => {
    // //按钮权限控制判断。 如果订餐交易订单存在， 则赋值，并不再向下执行
    // const _tradeOrder = store.Perms.some(v => v == tradeOrder)
    // if (_tradeOrder) {
    //     state.isEmpty = false
    //     state.orderType = 'classDimension'
    //     state.components = classDimension
    // } else {
    //     // 如果订餐退费订单存在， 则赋值，并不再向下执行
    //     const _refundOrder = store.Perms.some(v => v == refundOrder)
    //     if (_refundOrder) {
    //         state.isEmpty = false
    //         state.orderType = 'packageDimension'
    //         state.components = packageDimension
    //     } else {
    //         state.isEmpty = true
    //     }
    // }
})
</script>

<style lang="less" scoped>
.package-statistics {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            text-align: left;
            font-style: normal;
            flex: 1;
            :deep(.ant-tabs-nav) {
                margin: 0;
                .ant-tabs-nav-wrap {
                    height: 62px;
                }
            }
        }
    }

    .pageBody {
        padding: 20px;
    }
}
</style>
