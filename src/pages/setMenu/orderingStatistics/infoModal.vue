<template>
    <YModal v-model:open="state.mealDetailsModel" title="详情" width="1300px" :footer="null" @cancel="handleCancel">
        <div style="padding: 24px">
            <div class="radio_group">
                <a-radio-group @change="handlerActiveKey" v-model:value="form.servingStatus" button-style="solid">
                    <a-radio-button :value="1">已就餐</a-radio-button>
                    <a-radio-button :value="0">未就餐</a-radio-button>
                    <a-radio-button :value="2">已取消</a-radio-button>
                </a-radio-group>
            </div>
            <ETable
                :columns="columns"
                :minH="450"
                :data-source="state.dataSource"
                :paginations="state.pagination"
                :loading="state.tabLoading"
                @change="handleTableChange"
            >
                <template #bodyCell="{ column, record, index }">
                    <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                    <template v-if="column.dataIndex == 'operate'">
                        <a-button type="link" class="btn-link-color" @click="handleDetails(record)">详情</a-button>
                    </template>
                </template>
            </ETable>
        </div>
    </YModal>
    <YModal v-model:open="state.mealInfoModel" title="套餐详情" width="860px" :footer="null" @cancel="infoCancel">
        <div style="padding: 24px">
            <a-row :gutter="[16, 18]">
                <YCol :span="8">
                    {{ selectItem.userName || '-' }}
                </YCol>
                <YCol :span="8">
                    {{ selectItem.orgName || '-' }}
                </YCol>
                <YCol :span="8"></YCol>
                <YCol :span="8">套餐类型：{{ mealDetails.mealSetTypeName || '-' }}</YCol>
                <YCol :span="8">套餐名称：{{ mealDetails.mealSetVersionName || '-' }}</YCol>
                <YCol :span="8">套餐价格：{{ mealDetails.totalPrice || '-' }}</YCol>
                <YCol :span="24">
                    <div class="meal_details_content">
                        <span>菜品名称：</span>
                        <div class="food_list" v-if="mealDetails.dishList && mealDetails.dishList.length">
                            <a-row :gutter="[16, 18]" style="width: 100%">
                                <YCol :span="8" v-for="item in mealDetails.dishList" :key="item.id">
                                    <div class="food_item">
                                        <img class="food_img" :src="item.dishLogo" alt="" />
                                        <span class="name">{{ item.dishName }}</span>
                                        <span class="dish_price">¥{{ item.dishPrice }}</span>
                                    </div>
                                </YCol>
                            </a-row>
                        </div>
                        <div v-else>-</div>
                    </div>
                </YCol>
                <YCol :span="24">
                    <div class="meal_details_content">
                        <span>额外菜品：</span>
                        <div class="food_list" v-if="mealDetails.singleDishList && mealDetails.singleDishList.length">
                            <a-row :gutter="[16, 18]" style="width: 100%">
                                <YCol :span="8" v-for="item in mealDetails.singleDishList" :key="item.id">
                                    <div class="food_item">
                                        <img class="food_img" :src="item.dishLogo" alt="" />
                                        <span class="name">{{ item.dishName }}</span>
                                        <span class="dish_price">¥{{ item.dishPrice }}</span>
                                    </div>
                                </YCol>
                            </a-row>
                        </div>
                        <div v-else>-</div>
                    </div>
                </YCol>
                <YCol :span="8">点餐时间：{{ selectItem.createTime || '-' }}</YCol>
                <YCol :span="8">就餐状态：{{ selectItem.servingStatusName || '-' }}</YCol>
                <YCol :span="8">就餐时间：{{ selectItem.servingTime || '-' }}</YCol>
            </a-row>
        </div>
    </YModal>
</template>

<script setup>
import Http from '@/utils/http.js'

const showMoal = (data, type, mealSetTypeId) => {
    form.value.startTime = data.orderDate
    form.value.endTime = data.orderDate
    form.value.mealSetTypeId = mealSetTypeId
    activeKey.value = type
    form.value.servingStatus = 1
    state.mealDetailsModel = true
    getList()
}

const activeKey = ref('student')

const mealDetails = ref({})
const selectItem = ref({})
const state = reactive({
    mealDetailsModel: false,
    mealInfoModel: false,
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    tabLoading: false,
    exportLoading: false,
    dataSource: [],
    sort: {
        orderField: '',
        orderSort: '',
    },
})

const form = ref({
    servingStatus: '1',
})

const columns = computed(() => {
    return [
        { title: '序号', dataIndex: 'index', width: 80 },
        { title: '姓名', dataIndex: 'userName', key: 'userName' },
        { title: activeKey.value == 'student' ? '班级' : '部门', dataIndex: 'orgName', key: 'orgName' },
        { title: '套餐类型', dataIndex: 'mealSetTypeName', key: 'mealSetTypeName' },
        { title: '套餐名称', dataIndex: 'mealSetVersionName', key: 'mealSetVersionName' },
        { title: '价格', dataIndex: 'totalPrice', key: 'totalPrice' },
        { title: '点餐模式', dataIndex: 'orderModeName', key: 'orderModeName' },
        { title: '预定日期', dataIndex: 'orderDate', key: 'orderDate' },
        { title: '点餐时间', dataIndex: 'createTime', key: 'createTime', sorter: true },
        { title: '就餐状态', dataIndex: 'servingStatusName', key: 'servingStatusName' },
        { title: '就餐时间', dataIndex: 'servingTime', key: 'servingTime', sorter: true },
        { title: '操作', dataIndex: 'operate', key: 'operate' },
    ]
})

const submit = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getList()
}

const reset = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    state.sort = {
        orderField: '',
        orderSort: '',
    }
    getList()
}

function handleCancel() {
    state.mealDetailsModel = false
}

const infoCancel = () => {
    state.mealInfoModel = false
}

const handlerActiveKey = key => {
    reset()
}

// 分页
const handleTableChange = ({ current, pageSize, total }, filters, { field, order }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    state.sort.field = ''
    state.sort.order = ''
    if (order) {
        state.sort.orderField = field == 'createTime' ? 'create_time' : 'serving_time'
        state.sort.orderSort = order == 'ascend' ? 'asc' : 'desc'
    }
    getList()
}

function getList() {
    state.tabLoading = true
    const params = {
        ...state.pagination,
        ...state.sort,
        ...form.value,
        userType: activeKey.value == 'student' ? 0 : 1,
    }
    Http.post('/cloud/canteenStudentOrder/selectOrderRecordPage', params)
        .then(({ data }) => {
            state.dataSource = data.list
            state.pagination.pageNo = data.pageNo || 1
            state.pagination.pageSize = data.pageSize || 10
            state.pagination.total = data.total
        })
        .finally(() => {
            state.tabLoading = false
        })
}

function handleDetails(item) {
    state.mealInfoModel = true
    selectItem.value = item
    Http.get('/cloud/canteenStudentOrder/get', { id: item.id }).then(res => {
        mealDetails.value = res.data
    })
    console.log(mealDetails.value, 'itemitemitemitem')
}

defineExpose({
    showMoal,
})
</script>
<style lang="less" scoped>
.radio_group {
    padding-bottom: 16px;
}

.meal_details_content {
    display: flex;
    flex-wrap: wrap;
    .food_list {
        display: flex;
        flex-wrap: wrap;
        flex: 1;
    }
}
.food_item {
    margin-right: 20px;
    display: flex;
    flex-direction: column;
    .food_img {
        width: 86px;
        height: 86px;
        border-radius: 4px;
    }
    .name {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin: 8px 0px 4px 0px;
    }
    .dish_price {
        font-weight: 400;
        font-size: 14px;
        color: #999999;
        line-height: 20px;
    }
}
</style>
