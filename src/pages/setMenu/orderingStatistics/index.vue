<template>
    <div class="package-statistics">
        <div class="pageHead">
            <div class="pageHead_title">
                <a-tabs v-model:activeKey="activeKey" @change="handlerActiveKey">
                    <a-tab-pane v-for="item in mealSetTypes" :key="item.id" :tab="item.name"></a-tab-pane>
                </a-tabs>
            </div>
        </div>
        <div class="pageBody">
            <div class="ordering_record">
                <searchForm class="search_form" v-model:formState="form" :formList="formList" @submit="submit" @reset="reset">
                    <template #startEndRefundTime>
                        <a-range-picker
                            v-model:value="form.startEndRefundTime"
                            format="YYYY-MM-DD"
                            :allowClear="false"
                            valueFormat="YYYY-MM-DD"
                            @calendarChange="onCalendarChange"
                            :presets="servingTimePresets"
                        />
                    </template>
                </searchForm>
                <div class="btn_group">
                    <a-button @click="handleExport" :loading="state.exportLoading">导出</a-button>
                </div>
                <ETable
                    :columns="columns"
                    :minH="450"
                    :data-source="state.dataSource"
                    :paginations="state.pagination"
                    :loading="state.tabLoading"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                        <template v-if="column.dataIndex == 'breakfastCount'">
                            <span class="user_name_info" @click="handleInfo(record, column)">{{ record.breakfastCount }}</span>
                        </template>
                        <template v-if="column.dataIndex == 'lunchCount'">
                            <span class="user_name_info" @click="handleInfo(record, column)">{{ record.lunchCount }}</span>
                        </template>
                        <template v-if="column.dataIndex == 'dinnerCount'">
                            <span class="user_name_info" @click="handleInfo(record, column)">{{ record.dinnerCount }}</span>
                        </template>
                        <template v-if="column.dataIndex == 'snackCount'">
                            <span class="user_name_info" @click="handleInfo(record, column)">{{ record.snackCount }}</span>
                        </template>
                    </template>
                </ETable>
            </div>
        </div>
    </div>
    <InfoModal ref="infoModalRef" />
</template>

<script setup>
import dayjs from 'dayjs'
import Http from '@/utils/http.js'
import InfoModal from './infoModal.vue'
const activeKey = ref('student')
const infoModalRef = ref(null)

function extractMealId(obj, str) {
    const mapping = {
        breakfastCount: 'BRK',
        lunchCount: 'LUN',
        dinnerCount: 'DIN',
        snackCount: 'SNK',
    }

    const prefix = mapping[obj.dataIndex] || mapping[obj.key]
    if (!prefix) return null

    const regex = new RegExp(`${prefix}#-#(\\d+)#-#`)
    const match = str.match(regex)

    return match ? match[1] : null
}

const handleInfo = (record, column) => {
    const mealSetTypeId = extractMealId(column, record.dine)
    infoModalRef.value.showMoal(record, activeKey.value, mealSetTypeId)
}

const mealSetTypes = [
    {
        id: 'student',
        name: '学生',
    },
    {
        id: 'teacher',
        name: '老师',
    },
]

const state = reactive({
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    tabLoading: false,
    exportLoading: false,
    dataSource: [],
})

const form = ref({})

const columns = computed(() => {
    return [
        { title: '序号', dataIndex: 'index', width: 80 },
        { title: '日期', dataIndex: 'orderDate', key: 'orderDate' },
        { title: '早餐', dataIndex: 'breakfastCount', key: 'breakfastCount' },
        { title: '中餐', dataIndex: 'lunchCount', key: 'lunchCount' },
        { title: '晚餐', dataIndex: 'dinnerCount', key: 'dinnerCount' },
        { title: '点心', dataIndex: 'snackCount', key: 'snackCount' },
    ]
})

/**
 * 就餐时间快捷预设，提供“最近一周 / 最近一个月 / 最近三个月”快速筛选
 */
const servingTimePresets = computed(() => {
    const now = dayjs()
    const weekStart = dayjs().weekday(1).startOf('day')
    const weekEnd = dayjs().weekday(7).endOf('day')
    const monthStart = now.startOf('month').startOf('day')
    const monthEnd = now.endOf('month').endOf('day')
    const threeMonthsStart = now.subtract(2, 'month').startOf('month').startOf('day')
    return [
        { label: '最近一周', value: [weekStart, weekEnd] },
        { label: '最近一个月', value: [monthStart, monthEnd] },
        { label: '最近三个月', value: [threeMonthsStart, monthEnd] },
    ]
})

const formList = computed(() => {
    return [
        {
            type: 'rangePicker',
            label: '日期',
            value: ['startTime', 'endTime'],
            attrs: {
                presets: servingTimePresets.value,
            },
        },
    ]
})
const submit = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getList()
}

const reset = () => {
    form.value = {}
    submit()
}

const handlerActiveKey = key => {
    activeKey.value = key
    reset()
}

// 分页
const handleTableChange = ({ current, pageSize, total }, filters, { field, order }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    getList()
}

function getList() {
    state.tabLoading = true
    const params = {
        ...state.pagination,
        ...form.value,
        userType: activeKey.value == 'student' ? 0 : 1,
    }
    Http.post('/cloud/canteenStudentOrder/dineStatisticsPage', params)
        .then(({ data }) => {
            state.dataSource = data.list
            state.pagination.pageNo = data.pageNo || 1
            state.pagination.pageSize = data.pageSize || 10
            state.pagination.total = data.total
        })
        .finally(() => {
            state.tabLoading = false
        })
}

const onCalendarChange = val => {
    form.value.startEndRefundTime = val
    form.value.startTime = val ? val[0] : ''
    form.value.endTime = val ? val[1] : ''
}

const handleExport = async () => {
    try {
        state.exportLoading = true
        const title = `${activeKey.value == 'student' ? '学生' : '老师'}点餐统计.xlsx`
        Http.download(
            '/cloud/canteenStudentOrder/orderRecordExport',
            'post',
            {
                userType: activeKey.value == 'student' ? 0 : 1,
            },
            title,
        )
    } catch (error) {
        YMessage.error('导出失败：' + error.message)
    } finally {
        state.exportLoading = false
    }
}

onMounted(() => {
    getList()
})
</script>

<style lang="less" scoped>
.package-statistics {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            text-align: left;
            font-style: normal;
            flex: 1;
            :deep(.ant-tabs-nav) {
                margin: 0;
                .ant-tabs-nav-wrap {
                    height: 62px;
                }
            }
        }
    }

    .pageBody {
        padding: 20px;
    }
}
.ordering_record {
    .search_form {
        margin: 20px 0;
    }
}
.btn_group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 18px 0;
}

.user_name_info {
    cursor: pointer;
    color: #00b781;
}
</style>
