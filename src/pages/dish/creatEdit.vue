<template>
    <div class="dish-creat-edit">
        <div class="pageHead">
            <div class="pageHead_title">
                <ArrowLeftOutlined class="back" @click="handleBack" :style="{ color: store.sysColor }" />
                {{ state.formState.isDetail ? '菜品详情' : state.formState.id ? '编辑菜品' : '新增菜品' }}
            </div>
        </div>
        <div class="pageBody">
            <div class="create-form">
                <a-form :model="state.formState" ref="formRef" layout="vertical" :rules="merchantFormRule">
                    <a-form-item label="菜品图片：" name="logo">
                        <UploadImage
                            @emitFileList="handlerEmitFileList"
                            :disabled="isDisabled"
                            :sizeNum="2"
                            :feedbackFileList="state.feedbackFileList"
                            accept="image/*"
                            tips="最多上传1张，建议上传图片为400px×400px，支持图片格式为png/jpg/bmp/webp,大小不超过2M"
                        />
                    </a-form-item>
                    <a-form-item label="菜品名称：" name="name">
                        <a-input
                            v-model:value.trim="state.formState.name"
                            show-count
                            :maxlength="20"
                            placeholder="请输入"
                            :disabled="isDisabled"
                        />
                    </a-form-item>

                    <a-form-item label="菜品分类：" name="dishTypeId">
                        <a-select
                            :disabled="isDisabled"
                            v-model:value="state.formState.dishTypeId"
                            style="width: 100%"
                            placeholder="请选择"
                            show-search
                            :options="state.modelOptions"
                            :fieldNames="{ label: 'name', value: 'id' }"
                            :filter-option="filterOption"
                        ></a-select>
                    </a-form-item>

                    <a-form-item label="菜品价格：" name="price">
                        <a-input-number
                            style="width: 100%"
                            :step="0.01"
                            :min="0.0"
                            :max="9999"
                            string-mode
                            v-model:value.trim="state.formState.price"
                            placeholder="请输入价格（保留两位小数点，如9.99）"
                            addon-after="元"
                            :disabled="isDisabled"
                            @change="handlePriceChange"
                        ></a-input-number>
                    </a-form-item>

                    <a-form-item label="菜品状态：" name="enabled">
                        <a-radio-group v-model:value="state.formState.enabled" :disabled="isDisabled">
                            <a-radio :value="true">启用</a-radio>
                            <a-radio :value="false">禁用</a-radio>
                        </a-radio-group>
                    </a-form-item>
                </a-form>
            </div>
        </div>
        <div class="footer" v-if="!isDisabled">
            <a-button @click="handleBack">取消</a-button>
            <a-button type="primary" :loading="state.loading" @click="submit">确定</a-button>
        </div>
    </div>
</template>

<script setup>
const store = useStore()
const props = defineProps({
    paramsEdit: {
        type: Object,
        default: () => ({}),
    },
    deviceType: {
        type: String,
        default: 'device',
    },
    isDisabled: {
        // 是否禁用
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['update:isDishType', 'emitInitData'])

const state = reactive({
    feedbackFileList: [],
    modelOptions: [],
    loading: false,
    formState: {
        id: '',
        name: null,
        logo: '',
        dishTypeId: null,
        price: 0,
        enabled: true,
    },
})

const merchantFormRule = {
    name: [{ required: true, message: '请输入' }],
    logo: [{ required: true, message: '请选择' }],
    price: [{ required: true, message: '请输入' }],
    dishTypeId: [{ required: true, message: '请选择' }],
    enabled: [{ required: true, message: '请选择' }],
}
// 返回
const handleBack = () => {
    emit('update:isDishType', false)
    state.formState = { price: 0 }
    emit('emitInitData')
}
const formRef = ref(null)
// 设备提交编辑
const submit = () => {
    formRef.value
        .validate()
        .then(() => {
            //  新增菜品
            let API = '/cloud/canteenDish/create'
            if (state.formState.id) {
                // 编辑菜品
                API = '/cloud/canteenDish/update'
            }
            state.loading = true
            http.post(API, state.formState)
                .then(({ message }) => {
                    YMessage.success(message)
                    handleBack()
                })
                .finally(() => (state.loading = false))
        })
        .catch(err => {
            console.log('err:', err)
        })
}
// 获取菜品菜品类型
const canteenDishTypeList = () => {
    http.post('/cloud/canteenDishType/list', {}).then(({ data }) => {
        state.modelOptions = data
    })
}

// 更新图片
const handlerEmitFileList = list => {
    state.formState.logo = list[0]?.url || ''
    formRef.value.validateFields('logo')
}

const handlePriceChange = value => {
    // 将输入的值转换为数字并保留两位小数
    const num = parseFloat(value)
    if (!isNaN(num)) {
        state.formState.price = num.toFixed(2)
    } else {
        state.formState.price = ''
    }
}
const filterOption = (input, option) => {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
onMounted(() => {
    if (props.paramsEdit?.id) {
        state.formState = props.paramsEdit
        state.feedbackFileList = [
            {
                url: props.paramsEdit.logo,
                name: props.paramsEdit.name,
            },
        ]
    }
    canteenDishTypeList()
})
</script>

<style lang="less" scoped>
.dish-creat-edit {
    position: relative;
    height: calc(100vh - 99px);

    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;

            .back {
                font-size: 16px;
            }
        }
    }

    .pageBody {
        padding: 20px;
        overflow-y: auto;
        height: calc(100vh - 233px);

        .create-form {
            width: 65%;
            margin: 0 auto;

            :deep(.ant-form-item) {
                margin-bottom: 20px;
            }
        }
    }

    .footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        z-index: 999;
        border-top: 1px solid @border-color-base;
        background-color: @body-background;
        padding: 20px 0;
    }
}
</style>
