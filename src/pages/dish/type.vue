<!-- 菜品 -->
<template>
    <div class="dish-type">
        <div class="pageHead">
            <div class="pageHead_title">
                <ArrowLeftOutlined class="back" @click="handleBack" :style="{ color: store.sysColor }" />
                菜品类型管理
            </div>
        </div>
        <div class="pageBody">
            <div class="create-btn">
                <a-button type="primary" v-auth="'canteenMachine.dish.typeAdd'" @click="handleAddEdit({})">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建分类
                </a-button>
            </div>
            <ETable
                :columns="columns"
                :data-source="state.dataSource"
                :paginations="state.pagination"
                :loading="state.loading"
                @change="handleTableChange"
            >
                <template #bodyCell="{ column, text, record, index }">
                    <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                    <template v-else-if="column.dataIndex == 'isBinding'">
                        {{ record.isBinding == 1 ? '未绑定' : '已绑定' }}
                    </template>

                    <template v-else-if="column.dataIndex == 'operate'">
                        <a-button
                            type="link"
                            class="btn-link-color"
                            v-auth="'canteenMachine.dish.typeEdit'"
                            @click="handleAddEdit(record)"
                        >
                            编辑
                        </a-button>
                        <a-button
                            type="link"
                            danger
                            class="btn-link-color"
                            v-auth="'canteenMachine.dish.typeDelete'"
                            @click="handleDelete(record.id)"
                        >
                            删除
                        </a-button>
                    </template>
                    <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip>
                </template>
            </ETable>
        </div>
        <YModal
            v-model:open="state.editDishTypeOpen"
            :title="state.formState.id ? '编辑分类' : '新建分类'"
            :bodyStyle="bodyStyle"
            @ok="handleOk"
        >
            <a-form :model="state.formState" ref="formRef" layout="vertical" :rules="merchantFormRule">
                <a-form-item label="类型名称：" name="name">
                    <a-input v-model:value.trim="state.formState.name" show-count :maxlength="8" placeholder="请输入类型名称" />
                </a-form-item>
            </a-form>
            <template #footer>
                <a-button key="back" @click="handleCancel">取消</a-button>
                <a-button key="submit" type="primary" :loading="state.loading" @click="handleOk">确定</a-button>
            </template>
        </YModal>
    </div>
</template>

<script setup>
import { reactive, onMounted, ref } from 'vue'
import Http from '@/utils/http.js'
const store = useStore()
const bodyStyle = { padding: '24px' }
const merchantFormRule = {
    name: [{ required: true, message: '请输入' }],
}
const columns = [
    { title: '序号', dataIndex: 'index', key: 'index' },
    { title: '类型名称', dataIndex: 'name', key: 'name' },
    { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime', sorter: true },
    { title: '操作', dataIndex: 'operate', key: 'operate', width: 120 },
]
const props = defineProps({
    isDishType: {
        type: Boolean,
        default: false,
    },
    dishId: {
        type: String,
        default: '',
    },
})

const formRef = ref(null)
const emit = defineEmits(['update:isDishType'])
const state = reactive({
    editDishTypeOpen: false,
    formState: { id: '', name: '' },
    loading: false,
    dataSource: [],
    orderByUpdateTimeDesc: true,
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
})
// 获取菜品类型-列表
const getInitData = async () => {
    try {
        state.loading = true
        const { orderByUpdateTimeDesc, pagination } = state
        const params = {
            ...pagination,
            orderByUpdateTimeDesc,
        }
        await Http.post('/cloud/canteenDishType/page', params).then(({ data }) => {
            const { pageNo, pageSize, total, list } = data
            state.dataSource = list
            state.pagination.pageNo = pageNo
            state.pagination.pageSize = pageSize
            state.pagination.total = total
        })
    } catch (error) {
        console.log('getList error: ', error)
    } finally {
        state.loading = false
    }
}

// 分页
const handleTableChange = ({ current, pageSize, total }, filters, { field, order }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    state.orderByUpdateTimeDesc = false
    if (order) {
        state.orderByUpdateTimeDesc = order !== 'ascend'
    }
    getInitData()
}

// 新增编辑菜单类型
const handleAddEdit = (item = {}) => {
    state.formState.id = item.id || ''
    state.formState.name = item.name || ''
    state.editDishTypeOpen = true
}
// 取消
const handleCancel = () => {
    state.editDishTypeOpen = false
}

// 新建类型确定
const handleOk = () => {
    formRef.value
        .validate()
        .then(() => {
            state.loading = true
            let API = '/cloud/canteenDishType/create'
            if (state.formState.id) {
                API = '/cloud/canteenDishType/update'
            }
            Http.post(API, state.formState)
                .then(({ message }) => {
                    YMessage.success(message)
                    handleCancel()
                    getInitData()
                })
                .finally(() => (state.loading = false))
        })
        .catch(err => {
            console.log('err:', err)
        })
}

// 删除菜品类型
const handleDelete = id => {
    yConfirm('确认删除', '确认删除当前菜品类型？', '确 认', '取 消').then(res => {
        res &&
            Http.post('/cloud/canteenDishType/delete', { ids: [id] }).then(({ message }) => {
                YMessage.success(message)
                getInitData()
            })
    })
}
// 返回
const handleBack = () => {
    emit('update:isDishType', false)
}
onMounted(() => {
    getInitData()
})
</script>

<style lang="less" scoped>
.dish-type {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;
            .back {
                font-size: 16px;
            }
        }
    }

    .pageBody {
        padding: 20px;

        .create-btn {
            text-align: right;
            margin-bottom: 20px;
        }
    }

    :deep(.ant-table-column-has-sorters) {
        .ant-table-column-sorters {
            justify-content: flex-start;
            .ant-table-column-title {
                flex: none;
            }
        }
    }
}
</style>
