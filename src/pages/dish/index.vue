<!-- 菜品 -->
<template>
    <div class="dish">
        <template v-if="!state.isDishType">
            <div class="pageHead">
                <div class="pageHead_title">菜品管理</div>
            </div>
            <div class="pageBody">
                <searchForm v-model:formState="state.form" :formList="formList" @submit="queryInitData" @reset="queryInitData">
                    <template #dishTypeId>
                        <a-select
                            v-model:value="state.form.dishTypeId"
                            show-search
                            placeholder="请选择"
                            :options="dishTypeList"
                            :fieldNames="{ label: 'name', value: 'id' }"
                            :filter-option="filterOption"
                        ></a-select>
                    </template>
                </searchForm>
                <div class="create-btn">
                    <a-button type="primary" v-auth="'canteenMachine.dish.add'" @click="handleCreatEdit(null)">
                        <template #icon>
                            <PlusOutlined />
                        </template>
                        新建菜品
                    </a-button>
                    <a-button v-auth="'canteenMachine.dish.type'" @click="handleDishType">菜品类型管理</a-button>
                    <a-button v-auth="'canteenMachine.dish.import'" @click="handleImport">导入菜品</a-button>
                    <a-button :disabled="!state.selectedRowKeys.length" @click="handleDelete(state.selectedRowKeys)">
                        批量删除
                    </a-button>
                    <a-dropdown>
                        <template #overlay>
                            <a-menu>
                                <a-menu-item @click="handleDisable(state.selectedRowKeys, false)">批量启用</a-menu-item>
                                <a-menu-item @click="handleDisable(state.selectedRowKeys, true)">批量禁用</a-menu-item>
                            </a-menu>
                        </template>
                        <a-button :disabled="!state.selectedRowKeys.length">批量启用/禁用</a-button>
                    </a-dropdown>
                </div>
                <ETable
                    :columns="columns"
                    :minH="450"
                    :data-source="state.dataSource"
                    :paginations="state.pagination"
                    :loading="state.loading"
                    @change="handleTableChange"
                    :row-selection="{
                        selectedRowKeys: state.selectedRowKeys,
                        onChange: (selectedRowKeys, keyObj) => {
                            state.selectedRowKeys = selectedRowKeys
                            state.keyObj = keyObj
                        },
                    }"
                >
                    <template #bodyCell="{ column, text, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                        <template v-else-if="column.dataIndex == 'enabled'">
                            {{ record.enabled ? '启用' : '禁用' }}
                        </template>

                        <template v-else-if="column.dataIndex == 'operate'">
                            <a-button
                                type="link"
                                class="btn-link-color"
                                v-auth="'canteenMachine.dish.disabled'"
                                @click="handleDisable([record.id], record.enabled)"
                            >
                                {{ record.enabled ? '禁用' : '启用' }}
                            </a-button>
                            <a-button
                                type="link"
                                class="btn-link-color"
                                v-auth="'canteenMachine.dish.details'"
                                @click="handleCreatEdit(record, true)"
                            >
                                详情
                            </a-button>
                            <a-button
                                v-if="!record.enabled"
                                type="link"
                                class="btn-link-color"
                                v-auth="'canteenMachine.dish.edit'"
                                @click="handleCreatEdit(record)"
                            >
                                编辑
                            </a-button>
                            <a-button
                                v-if="state.selectedRowKeys.includes(record.id) && !record.enabled"
                                type="link"
                                class="btn-link-delete"
                                @click="handleDelete([record.id])"
                            >
                                删除
                            </a-button>
                        </template>
                        <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip>
                    </template>
                </ETable>
            </div>
        </template>
        <component
            v-else
            v-model:isDishType="state.isDishType"
            :is="state.components"
            :paramsEdit="state.paramsEdit"
            :isDisabled="state.isDisabled"
            @emitInitData="getInitData"
        />
        <YImport
            v-model:show="state.isImport"
            uploadUrl="/cloud/canteenDish/importDish"
            :progressRequest="progressRequest"
            :errorExport="errorExport"
            :title="state.importTitle"
            code="canteenDishColumn"
        ></YImport>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, defineAsyncComponent } from 'vue'
import Http from '@/utils/http.js'
const CreatEdit = defineAsyncComponent(() => import('./creatEdit.vue'))
const Type = defineAsyncComponent(() => import('./type.vue'))
const formList = ref([
    {
        type: 'input',
        value: 'name',
        label: '菜品名称',
    },
    {
        type: 'slot',
        value: 'dishTypeId',
        label: '菜品类型',
        list: [],
    },
    {
        type: 'select',
        value: 'enabled',
        label: '菜品状态',
        list: [
            { label: '全部', value: null },
            { label: '启用', value: '1' },
            { label: '禁用', value: '0' },
        ],
    },
])
const columns = [
    { title: '序号', dataIndex: 'index', key: 'index' },
    { title: '菜品名称', dataIndex: 'name', key: 'name', width: 200 },
    { title: '价格（元）', dataIndex: 'price', key: 'price' },
    { title: '菜品类型', dataIndex: 'dishTypeName', key: 'dishTypeName', sorter: true },
    { title: '菜品状态', dataIndex: 'enabled', key: 'enabled' },
    { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime', sorter: true },
    { title: '操作', dataIndex: 'operate', key: 'operate', width: 200 },
]
const dishTypeList = ref([])
const state = reactive({
    isImport: false,
    importTitle: '',
    components: null,
    paramsEdit: {},
    isDishType: false,
    isDisabled: false,

    loading: false,
    dataSource: [],
    selectedRowKeys: [],
    keyObj: [],
    form: {
        name: '',
        dishTypeId: null,
        enabled: null,
    },
    sort: { updateTime: '', dishTypeName: '' },
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
})

const getInitData = async () => {
    try {
        state.selectedRowKeys = []
        state.keyObj = []
        state.loading = true
        const params = {
            ...state.form,
            ...state.pagination,
            sort: state.sort,
        }
        await Http.post('/cloud/canteenDish/page', params).then(({ data }) => {
            const { pageNo, pageSize, total, list } = data
            state.dataSource = list
            state.pagination.pageNo = pageNo
            state.pagination.pageSize = pageSize
            state.pagination.total = total
        })
    } catch (error) {
        console.log('getList error: ', error)
    } finally {
        state.loading = false
    }
}

// 菜品分页
const handleTableChange = ({ current, pageSize, total }, filters, { field, order }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    state.sort[`${field}`] = ''
    if (order) {
        state.sort[`${field}`] = order == 'ascend' ? 'asc' : 'desc'
    }
    getInitData()
}

// 查询重置
const queryInitData = item => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getInitData()
}

const handleDelete = ids => {
    if (!!ids.length) {
        yConfirm('提示', '确认删除选择的菜品吗？', '确 认', '取 消').then(res => {
            res &&
                Http.post('/cloud/canteenDish/delete', { ids }).then(({ message }) => {
                    YMessage.success(message)
                    queryInitData()
                })
        })
    } else {
        YMessage.warning('请选择确认要删除的菜品！')
    }
}

// 禁用菜品
const handleDisable = (ids, enabled) => {
    const isEnabled = state.keyObj.some(i => (!enabled ? i.enabled : !i.enabled))
    const title = !enabled ? '已启用' : '已禁用'
    if (isEnabled) {
        YMessage.warning(`请勿勾选${title}的菜品！`)
        return
    }
    if (!ids.length) {
        YMessage.warning('请选择需要修改状态的菜品！')
        return
    }
    const confirmTitle = !enabled ? '启用' : '禁用'
    yConfirm('提示', `确认${confirmTitle}选择的菜品吗？`, '确 认', '取 消').then(res => {
        res &&
            Http.post('/cloud/canteenDish/updateStatus', {
                ids,
                enabled: !enabled,
            })
                .then(({ message }) => {
                    YMessage.success(message)
                    getInitData()
                })
                .finally(() => (state.loading = false))
    })
}

// 新建编辑菜品
const handleCreatEdit = (item = {}, type) => {
    state.paramsEdit = item
    state.components = CreatEdit
    state.isDishType = true
    // 如果是查看详情，则加点东西进去
    state.isDisabled = type
    if (type) {
        state.paramsEdit.isDetail = true
    }
}
// 菜品类型
const handleDishType = () => {
    state.isDishType = true
    state.paramsEdit = {}
    state.components = Type
}

// 获取菜品菜品类型
const canteenDishTypeList = async () => {
    await Http.post('/cloud/canteenDishType/list', {}).then(({ data }) => {
        dishTypeList.value = data || []
    })
}
// 请求进度条数据
async function progressRequest(importId) {
    const { data } = await Http.get(`/cloud/import/getSchedule/${importId}`)
    return data
}
// 异常数据下载
async function errorExport(importIds) {
    Http.download(`/cloud/import/exportErrorData/${importIds[0]}`, 'get', {}, '智慧点餐菜品异常数据.xlsx')
}
const filterOption = (input, option) => {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
// 导入菜品
const handleImport = () => {
    state.isImport = true
    state.importTitle = '智慧点餐菜品'
}
onMounted(async () => {
    await canteenDishTypeList()
    getInitData()
})
</script>

<style lang="less" scoped>
.dish {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            line-height: 25px;
            text-align: left;
            font-style: normal;
        }
    }

    .pageBody {
        padding: 20px;

        .create-btn {
            text-align: right;
            margin: 20px 0;
        }
    }
    :deep(.ant-table-column-has-sorters) {
        .ant-table-column-sorters {
            justify-content: flex-start;
            .ant-table-column-title {
                flex: none;
            }
        }
    }
}
</style>
