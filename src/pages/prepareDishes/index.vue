<!-- 备菜 -->
<template>
    <div class="prepare-dishes">
        <div class="pageHead">
            <div class="pageHead_title">
                <a-tabs v-model:activeKey="state.activeKey" @change="handlerActiveKey">
                    <a-tab-pane v-for="item in mealSetTypes" :key="item.id" :tab="item.name"></a-tab-pane>
                </a-tabs>
            </div>
        </div>
        <div class="pageBody">
            <a-radio-group class="date-query" v-model:value="state.orderType" @change="handlerOrderTypeTabs" button-style="solid">
                <a-radio-button value="day">日</a-radio-button>
                <a-radio-button value="week">周</a-radio-button>
                <a-radio-button value="month">月</a-radio-button>
            </a-radio-group>
            <searchForm v-model:formState="state.form" :formList="formList" @submit="queryInitData" @reset="queryInitData" mb-20>
                <template #dishTypeId>
                    <a-select
                        v-model:value="state.form.dishTypeId"
                        show-search
                        placeholder="请选择"
                        :options="state.prepareOptions"
                        :fieldNames="{ label: 'name', value: 'id' }"
                        :filter-option="filterOption"
                    ></a-select>
                </template>
                <template #rangePicker>
                    <a-date-picker
                        v-if="state.orderType == 'day'"
                        v-model:value="state.dayTime"
                        format="YYYY-MM-DD"
                        valueFormat="YYYY-MM-DD"
                    />

                    <a-date-picker
                        v-else-if="state.orderType == 'week'"
                        picker="week"
                        :allowClear="false"
                        v-model:value="state.weekTime"
                        placeholder="开始周 至 结束周"
                        :format="customWeekStartEndFormat"
                        @change="handleChangeRange"
                    />
                    <a-date-picker
                        v-else-if="state.orderType == 'month'"
                        v-model:value="state.monthTime"
                        format="YYYY-MM"
                        valueFormat="YYYY-MM"
                        picker="month"
                    />
                </template>
            </searchForm>
            <div class="create-btn">
                <div class="outlay-total">
                    <span class="outlay-total-num" v-if="state.activeKey">老师总数：{{ state.teacherTotal }}人</span>
                    <span class="outlay-total-num" v-else>学生总数：{{ state.studentTotal }}人</span>
                    <span class="outlay-total-num">已定餐数：{{ state.userOrderTotal }}人</span>
                </div>
                <a-button v-auth="'canteenMachine.prepareDishes.export'" @click="handleExport">导出</a-button>
            </div>
            <ETable
                :columns="columns"
                :minH="450"
                :data-source="state.dataSource"
                :paginations="state.pagination"
                :loading="state.loading"
                @change="handleTableChange"
            >
                <template #bodyCell="{ column, text, record, index }">
                    <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                    <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip>
                </template>
            </ETable>
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import Http from '@/utils/http.js'
import dayjs from 'dayjs'

const mealSetTypes = [
    {
        id: 0,
        name: '学生',
    },
    {
        id: 1,
        name: '老师',
    },
]

const formList = [
    {
        type: 'input',
        value: 'dishName',
        label: '菜品名称',
    },
    {
        type: 'slot',
        value: 'dishTypeId',
        label: '菜品类型',
        list: [
            { label: '全部', value: null },
            { label: '在线', value: 1 },
            { label: '离线', value: 0 },
        ],
        fieldNames: {
            label: 'label',
            value: 'value',
        },
    },
    {
        type: 'slot',
        value: 'rangePicker',
        label: '就餐时间',
        attrs: {
            valueFormat: 'YYYY-MM-DD',
        },
        span: 5,
    },
]
const columns = [
    { title: '序号', dataIndex: 'index', key: 'index' },
    { title: '菜品名称', dataIndex: 'dishName', key: 'dishName' },
    { title: '菜单类型', dataIndex: 'dishTypeName', key: 'dishTypeName' },
    { title: '订单总数', dataIndex: 'dishCount', key: 'dishCount', width: 100 },
]

const state = reactive({
    drawerOpen: false,
    paramsEdit: {},
    loading: false,
    studentTotal: 0,
    teacherTotal: 0,
    userOrderTotal: 0,
    dataSource: [],
    prepareOptions: [],
    dayTime: '',
    weekTime: null,
    monthTime: null,
    orderType: 'day',
    form: {
        dishName: '',
        dishTypeId: null,
        orderStartDate: '',
        orderEndDate: '',
    },
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    downloadTitle: '',
    activeKey: 0,
})
const dayjsWweekday = params => {
    if (state.orderType == 'week') {
        if (state.weekTime) {
            // 获取当前周从周二开始的第一天
            params.orderStartDate = dayjs(state.weekTime).weekday(1).format('YYYY-MM-DD')
            // 获取当前周的最后一天（以周二为起始，最后一天是周一）
            params.orderEndDate = dayjs(state.weekTime).weekday(7).format('YYYY-MM-DD')
            state.weekTime = dayjs(state.weekTime).weekday(1)
        } else {
            params.orderStartDate = dayjs().weekday(1).format('YYYY-MM-DD')
            params.orderEndDate = dayjs().weekday(7).format('YYYY-MM-DD')
            state.weekTime = dayjs().weekday(1)
        }
        state.downloadTitle = `${params.orderStartDate}至${params.orderEndDate}`
    } else if (state.orderType == 'month') {
        const monthTime = state.monthTime || dayjs().startOf('month').format('YYYY-MM')
        params.orderStartDate = monthTime
        params.orderEndDate = monthTime
        state.monthTime = monthTime
        state.downloadTitle = monthTime
    } else {
        const dayTime = state.dayTime || dayjs().format('YYYY-MM-DD')
        params.orderStartDate = dayTime
        params.orderEndDate = dayTime
        state.dayTime = dayTime
        state.downloadTitle = dayTime
    }
}

const getInitData = async () => {
    // 备菜管理-按日|周|月统计分页数据
    try {
        state.loading = true
        const params = {
            ...state.pagination,
            ...state.form,
            orderType: state.orderType,
            userType: state.activeKey,
        }
        dayjsWweekday(params)
        await Http.post('/cloud/canteenStudentOrder/statisticsByDate', params).then(({ data }) => {
            const {
                pageData: { list, pageNo, pageSize, total },
                studentTotal,
                teacherTotal,
                userOrderTotal,
            } = data
            state.dataSource = list
            state.pagination.pageNo = pageNo || 1
            state.pagination.pageSize = pageSize || 10
            state.pagination.total = total
            state.teacherTotal = teacherTotal
            state.studentTotal = studentTotal
            state.userOrderTotal = userOrderTotal
        })
    } catch (error) {
        console.log('getList error: ', error)
    } finally {
        state.loading = false
    }
}

// 分页
const handleTableChange = ({ current, pageSize, total }, filters, { field, order }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    state.form[`${field}Sort`] = order
    getInitData()
}
// 日月周切换
const handlerOrderTypeTabs = type => {
    getInitData()
}
// 查询重置
const queryInitData = item => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    // 重置
    if (!item) {
        state.dayTime = null
        state.weekTime = null
        state.monthTime = null
    }
    getInitData()
}
// 学生老师切换
const handlerActiveKey = () => {
    state.form.dishTypeId = null
    state.form.dishName = ''
    state.form.orderStartDate = ''
    state.form.orderEndDate = ''
    state.orderType = 'day'
    queryInitData('')
}
// 添加导出功能
const handleExport = async () => {
    try {
        state.loading = true
        const params = {
            ...state.form,
            ...state.pagination,
            orderType: state.orderType,
            userType: state.activeKey,
        }
        dayjsWweekday(params)
        const title = `${state.activeKey ? '班级' : '部门'}-智慧点餐${state.downloadTitle}备菜.xlsx`

        Http.download('/cloud/canteenStudentOrder/export', 'post', params, title)
    } catch (error) {
        YMessage.error('导出失败：' + error.message)
    } finally {
        state.loading = false
    }
}
const filterOption = (input, option) => {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 获取菜品菜品类型
const canteenDishTypeList = async () => {
    await Http.post('/cloud/canteenDishType/list', {}).then(({ data }) => {
        state.prepareOptions = data || []
    })
}

const customWeekStartEndFormat = (value, format) => {
    const data = `${dayjs(value)
        .weekday(1)
        .format(format || 'YYYY-MM-DD')} 至 ${dayjs(value)
        .weekday(7)
        .format(format || 'YYYY-MM-DD')}`
    return data
}
// 一周时间
const handleChangeRange = event => {
    const dataTime = customWeekStartEndFormat(event, 'YYYY-MM-DD')
    const [startTime, endTime] = dataTime.split(' 至 ')
    return { startTime, endTime }
}
onMounted(() => {
    getInitData()
    canteenDishTypeList()
})
</script>

<style lang="less" scoped>
.prepare-dishes {
    .pageHead {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 62px;
        border-bottom: 1px solid @border-color-base;

        .pageHead_title {
            font-weight: 600;
            font-size: 18px;
            text-align: left;
            font-style: normal;
            flex: 1;
            :deep(.ant-tabs-nav) {
                margin: 0;
                .ant-tabs-nav-wrap {
                    height: 62px;
                }
            }
        }
    }

    .pageBody {
        padding: 20px;

        .date-query {
            margin-bottom: 20px;
        }

        .create-btn {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .outlay-total {
                background-color: @acitve-background;
                margin: 20px 0px;
                padding: 10px;

                .outlay-total-num {
                    margin-right: 20px;
                }
            }
        }
    }
}
</style>
