import { defineStore } from 'pinia'
export const useStore = defineStore('main', {
    state: () => {
        return {
            userInfo: {},
            token: ``,
            refreshToken: null,
            spinning: false,
            remember: true,
            accountFrom: {},
            Perms: [],
            tags: [],
            collapsed: false,
            exTag: '',
            formSchema: [],
            formDetails: {},
            noReadCount: 0,
            sysColor: '#00B781',
            sysIdent: 'management', // user:用户端  agent:代理商端  management:管理端
            agreement: false,
            isAlarmList: false,
        }
    },
    persist: {
        enabled: true,
        key: 'fbl',
        storage: localStorage,
        // paths: ['Perms', 'sysColor', 'userInfo', 'token', 'sysIdent', 'refreshToken', 'remember', 'accountFrom'],
    },
    actions: {
        setToken(token, refreshToken) {
            this.token = token
            this.refreshToken = refreshToken
        },
        clearUser() {
            this.userInfo = {}
            this.token = null
            this.refreshToken = null
            this.accountFrom = {}
            this.Perms = []
            window.localStorage.clear()
        },
    },
})
