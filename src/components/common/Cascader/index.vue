<template>
    <div>
        <a-cascader
            :options="options"
            :placeholder="placeholder"
            :value="state.data"
            :changeOnSelect="changeOnSelect"
            @change="updateValue"
            :showSearch="{ filter }"
            v-bind="attrs"
        />
    </div>
</template>

<script setup>
// *********************
// Hooks Function
// *********************

const emit = defineEmits(['update:modelValue', 'update'])
const attrs = useAttrs()

const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    placeholder: {
        type: String,
        default: '请选择',
    },
    options: {
        type: Array,
        default: [],
    },
    changeOnSelect: {
        type: Boolean,
        default: true,
    },
})

const state = reactive({
    data: [],
})

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const updateValue = (value, selectedOptions) => {
    const data = value || []
    state.data = data
    emit('update:modelValue', data)
    emit('update', selectedOptions)
}

const filter = (inputValue, path) => {
    return path.some(option => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
}

// *********************
// Watch Function
// *********************

watch(
    () => props.modelValue,
    value => {
        state.data = value || []
    },
    { immediate: true },
)
</script>

<style lang="less" scoped></style>
