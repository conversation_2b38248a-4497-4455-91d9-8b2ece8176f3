<template>
    <div>
        <a-cascader
            :fieldNames="{ label: 'name', value: 'id', children: 'children' }"
            :options="state.options"
            :placeholder="placeholder"
            :value="state.region"
            change-on-select
            @change="updateValue"
            :showSearch="{ filter }"
            v-bind="attrs"
        />
    </div>
</template>

<script setup>
// *********************
// Hooks Function
// *********************

const emit = defineEmits(['update:modelValue', 'update'])
const attrs = useAttrs()

const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    placeholder: {
        type: String,
        default: '请选择省市区',
    },
})

const state = reactive({
    options: [],
    region: [],
})

// *********************
// Default Function
// *********************

const getRegionList = async () => {
    const { data } = await http.get('/marketing/marketingArea/list')
    state.options = data
}

// *********************
// Life Event Function
// *********************

getRegionList()

// *********************
// Service Function
// *********************

const updateValue = (value, selectedOptions) => {
    const region = value || []
    state.region = region
    const modelValue = value ? region.join(',') : value
    emit('update:modelValue', modelValue)
    emit('update', selectedOptions)
}

const filter = (inputValue, path) => {
    return path.some(option => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
}

watch(
    () => props.modelValue,
    value => {
        if (value && value !== state.region.join(',')) {
            state.region = value.split(',')
        } else if (!value) {
            state.region = []
        }
    },
    { immediate: true },
)
</script>

<style lang="less" scoped></style>
