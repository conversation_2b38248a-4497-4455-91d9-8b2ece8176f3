<template>
    <a-modal
        class="mSelect"
        :open="modelState.open"
        :destroyOnClose="true"
        :maskClosable="false"
        title="选择"
        width="688px"
        @cancel="onCancel"
        @ok="onOk"
    >
        <div class="mSelect-wrap" v-if="modelState.open">
            <div class="section" :class="{ active: !isShowSearch }">
                <!-- 搜索 -->
                <a-input-search
                    v-if="isShowSearch"
                    v-model:value.trim="state.name"
                    placeholder="请输入搜索的内容"
                    @search="handleSearch"
                    allow-clear
                />
                <div p-20 v-if="state.isSearchTable">
                    <SearchTable
                        :data="modelState.searchTable"
                        :loading="modelState.loading"
                        :single="activeTab.single"
                        :selectKeys="state.selectedRowKeys"
                        @change="onSelectChange"
                        @paginationChange="searchByPage"
                    />
                </div>
                <div v-else class="select-wrap">
                    <!-- 分类 -->
                    <div class="tabs">
                        <a-radio-group v-model:value="activeTabIndex" button-style="solid" @change="onTabsChange">
                            <a-radio-button v-for="(item, index) in props.tabs" :key="item.id" :value="index">
                                {{ item.tab }}
                            </a-radio-button>
                        </a-radio-group>
                    </div>
                    <!-- 面包屑 -->
                    <a-breadcrumb>
                        <template #separator>
                            <a-avatar shape="square" :src="arrow" :size="20"></a-avatar>
                        </template>
                        <template v-for="(item, index) in state.breadcrumbs" :key="item[fieldNames.value]">
                            <a-breadcrumb-item href="" @click="handleBreadcrumb(item, index)">
                                {{ item[fieldNames.label] }}
                            </a-breadcrumb-item>
                        </template>
                    </a-breadcrumb>
                    <!-- 源数据 -->
                    <div class="structures">
                        <a-spin :spinning="modelState.loading">
                            <template v-if="modelState.dataSource?.length">
                                <div class="row" mt-16 v-if="!activeTab.single">
                                    <a-checkbox class="check" :checked="checkAll" @change="onCheckAllChange">
                                        全选
                                    </a-checkbox>
                                </div>
                                <component
                                    :value="originCheckedList"
                                    style="width: 100%"
                                    :is="activeTab.single ? ARadio.Group : ACheckbox.Group"
                                >
                                    <div class="row tree" v-for="item in modelState.dataSource" :key="item.id">
                                        <component
                                            class="check"
                                            :class="{
                                                'check-visible': !isCheckVisible(item),
                                            }"
                                            :value="itemValue(item)"
                                            :is="activeTab.single ? ARadio : ACheckbox"
                                            :disabled="
                                                item.disabled || (!item.isaAuthority && type === SELECT_TYPE.DEPARTMENT)
                                            "
                                            name="check"
                                            @change="onCheckChange($event, item)"
                                        >
                                            <a-avatar
                                                shape="square"
                                                :src="(!isPerson(item) && structure) || ''"
                                                :size="36"
                                            >
                                                <span v-if="isPerson(item)">
                                                    {{ item.studentName?.slice(-2) || item.name?.slice(-2) }}
                                                </span>
                                            </a-avatar>
                                            <div class="cnt">
                                                <div class="label ellipsis" :title="item[fieldNames.label]">
                                                    {{ item[fieldNames.label] }}
                                                </div>
                                            </div>
                                        </component>
                                        <div class="more" v-if="isShowMore(item)">
                                            <span @click="handleMore($event, item)">下级</span>
                                        </div>
                                    </div>
                                </component>
                                <div
                                    v-if="modelState.pages.total > modelState.pages.pageNo * modelState.pages.pageSize"
                                    w-full
                                    text-center
                                    pb-20
                                >
                                    <a-button @click="getMore">获取更多</a-button>
                                </div>
                            </template>

                            <div v-else class="data-empty">
                                <img src="@/assets/images/select-empty-data.png" />
                                <p>暂时没有找到相关结果</p>
                            </div>
                        </a-spin>
                    </div>
                </div>
            </div>
            <SelectedArea
                :unitText="unitText"
                :list="state.selectedList"
                :type="type"
                :tabs="tabs"
                :fieldNames="fieldNames"
                @clear="clearAll"
                @deleteItem="deleteSelectedItem"
            />
        </div>
    </a-modal>
</template>

<script setup>
import structure from '@/assets/images/icon-structure.png'
import arrow from '@/assets/images/icon-arrow.png'
import { Checkbox as ACheckbox, Radio as ARadio } from 'ant-design-vue'
import { SELECT_TYPE, DISPLAY_MODE, TRIGGER_MAP } from './constants'
import SelectedArea from './SelectedArea.vue'
import SearchTable from './SearchTable.vue'

// *********************
// Hooks Function
// *********************

const props = defineProps({
    // 用法看上面
    tabs: {
        type: Array,
        default: () => [],
    },
    // 选择组件类型
    type: {
        type: String,
        default: SELECT_TYPE.DEPARTMENT,
    },
    // 已选择
    selected: {
        type: Array,
        default: () => [],
    },
    fieldNames: {
        type: Object,
        default: () => {
            return { label: 'name', value: 'id' }
        },
    },
    unitText: {
        type: String,
        require: false,
    },
})
const emit = defineEmits(['search', 'toggleTabs', 'toggleLevel', 'cancel', 'submit'])

// $ 关联弹窗 （提供外部修改的数据）
const modelState = reactive({
    open: false, // 显示弹框
    dataSource: [], // 左侧数据源
    loading: false, // loading
    searchTable: {
        // 选人搜索 table 中显示
        list: [],
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    pages: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
})

const state = reactive({
    isSearchTable: false,
    name: '',
    // 面包屑
    breadcrumbs: [
        {
            name: props.tabs[0]?.tab,
            id: 0,
            children: [],
        },
    ],
    selectedList: [...(props.selected || [])],
    selectedRowKeys: [],
    isSearch: false,
})

// *********************
// Service Function
// *********************

// 选择的表格数据
const onSelectChange = (ron, node) => {
    const { id: _type, single } = activeTab.value
    if (single) {
        // 需要区分_type，不能影响别的tab栏的数据
        const ids = modelState.searchTable.list?.map(item => item.id) || []
        // 找到当前table下的id
        const id = state.selectedRowKeys.find(id => ids.includes(id))
        const reservedList = state.selectedList.filter(item => item._type !== _type)
        const reservedKeys = reservedList.map(item => item.id)
        const selectNode = node.filter(row => row.id !== id).map(row => ({ ...row, _type }))
        state.selectedRowKeys = selectNode.length ? [...reservedKeys, selectNode[0].id] : reservedKeys
        state.selectedList = [...reservedList, ...selectNode]
    } else {
        // 只针对当前的table数据进行选择,保留不在这个table里的选中数据
        const ids = modelState.searchTable.list?.map(item => item.id) || []
        const reservedList = state.selectedList.filter(item => !ids.includes(item.id))
        const reservedKeys = reservedList.map(item => item.id)
        state.selectedRowKeys = [...reservedKeys, ...ron]
        const selectNode = node.map(v => ({ ...v, _type }))
        state.selectedList = [...reservedList, ...selectNode]
    }
}

const itemValue = computed(() => {
    return item => item[props.fieldNames.value]
})

// 当前选人业务
const activeTabIndex = ref(props.tabs.findIndex(item => item.checked) || 0)
const activeTab = computed(() => {
    return props.tabs[activeTabIndex.value]
})

// 判断是人还是其他 （有一个tab符合就显示人。 后续可增加条件拓展）
const isPerson = computed(() => {
    return item => {
        if ([SELECT_TYPE.PEOPLE, SELECT_TYPE.ALL].includes(props.type)) {
            // $ tab有一个符合就算是人（切换tab,保持头像必须不变）
            return props.tabs.some(tab => tab.personField?.value.includes(item[tab.personField?.key]))
        }
        return false
    }
})

const isCheckVisible = computed(() => {
    return item => {
        if (props.type === SELECT_TYPE.DEPARTMENT) {
            // 选部门
            return true
        } else if (props.type === SELECT_TYPE.PEOPLE) {
            // 选人
            const value = activeTab.value.personField?.value || []
            const isPeople = value.includes(item[activeTab.value.personField?.key])
            return isPeople
        } else if (props.type === SELECT_TYPE.CLASS) {
            // 班级、宿舍
            const value = activeTab.value.personField?.value || []
            const isClass = value.includes(item[activeTab.value.personField?.key])
            return isClass
        } else if (props.type === SELECT_TYPE.ALL) {
            // 选人和选部门
            return true
        }
    }
})

const searchByPage = (pageNo, pageSize) => {
    const params = {
        name: state.name,
        pageNo,
        pageSize,
    }
    const tabId = activeTab.value.id
    emit('search', tabId, params)
}

// 搜索
const handleSearch = () => {
    state.isSearch = true
    // $ 默认使用新的搜索展示框，如果指定旧的就用旧的
    const { searchOption = { displayMode: DISPLAY_MODE.NEW } } = activeTab.value
    const isOld = searchOption.displayMode === DISPLAY_MODE.OLD
    state.isSearchTable = !isOld && !!state.name
    // 旧的展示方式
    if (searchOption.displayMode === DISPLAY_MODE.OLD) {
        // 删除面包屑
        state.breadcrumbs.splice(1)
    }
    // 需要把选中的数据同步给table的select
    state.selectedRowKeys = state.selectedList.map(item => item.id)
    // $ 目前旧的搜索不支持分页
    if (state.name || searchOption.displayMode === DISPLAY_MODE.OLD) {
        // 新模式下name为空不会派发'search'因为数据
        searchByPage(1, modelState.pages.pageSize)
    }
    // 重置分页数据
    if (!state.name) {
        modelState.pages = {
            pageNo: 1,
            pageSize: 10,
            total: 0,
        }
    }
}

// 楼层
let isShowSearch = computed(() => {
    const { searchOption = {} } = activeTab.value
    return !!searchOption.show
})

// 面包屑点击
const handleBreadcrumb = (row = {}, index = 0) => {
    if (index === 0) modelState.pages.total = 0
    state.breadcrumbs.splice(index + 1)
    state.name = ''
    const options = {
        // 当前聚焦的tab唯一标识
        tabId: activeTab.value.id,
        index,
        // 触发标识
        trigger: TRIGGER_MAP.BREAD_CRUMBS,
    }
    emit('toggleLevel', toRaw(row), options, { pageNo: 1, pageSize: modelState.pages.pageSize })
}

// tab切换
const onTabsChange = () => {
    emit('toggleTabs', activeTab.value)
    state.name = ''
    // 初始化面包屑
    state.breadcrumbs = [
        {
            name: activeTab.value.tab,
            id: activeTab.value.id,
        },
    ]
}
// 下级
const handleMore = (e, row) => {
    state.isSearch = false
    e.preventDefault()
    state.name = ''
    const isChecked = state.breadcrumbs.some(v => v.id === row.id)
    !isChecked && state.breadcrumbs.push(row)
    const options = {
        // 当前聚焦的tab唯一标识
        tabId: activeTab.value.id,
        index: state.breadcrumbs.length - 1,
        // 触发标识
        trigger: TRIGGER_MAP.NEXT_MORE,
    }
    emit('toggleLevel', toRaw(row), options, { pageNo: 1, pageSize: modelState.pages.pageSize })
}

// 左侧选中
const originCheckedList = computed(() => {
    return activeTab.value.single ? state.selectedList[0]?.id : state.selectedList.map(item => item.id)
})

// 全选
const checkAll = computed(() => {
    if (activeTab.value.single) {
        return false
    }
    let selectableList = []
    if ([SELECT_TYPE.DEPARTMENT, SELECT_TYPE.ALL].includes(props.type)) {
        selectableList = modelState.dataSource
    } else if ([SELECT_TYPE.PEOPLE, SELECT_TYPE.CLASS].includes(props.type)) {
        const value = activeTab.value.personField?.value || []
        // 获取到人|班级|宿舍的列表
        selectableList = modelState.dataSource.filter(item => value.includes(item[activeTab.value.personField?.key]))
    }
    return !!selectableList.length && selectableList.every(item => originCheckedList.value.indexOf(item.id) > -1)
})

// 是否展示下一级
const isShowMore = computed(() => {
    return item => {
        if ([SELECT_TYPE.PEOPLE, SELECT_TYPE.CLASS, SELECT_TYPE.ALL].includes(props.type)) {
            // 人|班级|宿舍
            const value = activeTab.value.personField?.value || []
            const isPerson = value.includes(item[activeTab.value.personField?.key])
            return !isPerson
        }
        return item.children?.length
    }
})

// 全选事件
const onCheckAllChange = e => {
    let selectableList = []
    if ([SELECT_TYPE.DEPARTMENT, SELECT_TYPE.ALL].includes(props.type)) {
        selectableList = modelState.dataSource
    } else if ([SELECT_TYPE.CLASS, SELECT_TYPE.PEOPLE].includes(props.type)) {
        const value = activeTab.value.personField?.value || []
        selectableList = modelState.dataSource.filter(item => value.includes(item[activeTab.value.personField?.key]))
    }

    //  $ 用于区分tab,记录类型
    const _type = activeTab.value.id

    if (e.target.checked) {
        const selected = state.selectedList.map(item => item[props.fieldNames.value])
        selectableList.forEach(item => {
            if (!selected.includes(item[props.fieldNames.value])) {
                state.selectedList.push({ ...item, _type })
            }
        })
    } else {
        const ids = new Set(selectableList.map(item => item[props.fieldNames.value]))
        // 过滤在ids存在的
        state.selectedList = state.selectedList.filter(item => !ids.has(item[props.fieldNames.value]))
    }
}

// 单选
const onCheckChange = (e, row) => {
    //  $ 用于区分tab,记录类型
    const _type = activeTab.value.id
    if (activeTab.value.single) {
        // 单选
        state.selectedList = e.target.checked ? [{ ...row, _type }] : []
    } else {
        // 复选
        if (e.target.checked) {
            state.selectedList.push({ ...row, _type })
        } else {
            const index = state.selectedList.findIndex(
                item => item[props.fieldNames.value] == row[props.fieldNames.value],
            )
            ~index && state.selectedList.splice(index, 1)
        }
    }
}

const clearAll = () => {
    state.selectedList = []
    state.selectedRowKeys = []
}

// 清空
const resetSelect = () => {
    state.selectedList = [...(props.selected || [])]
    state.selectedRowKeys = []
}

// 删除
const deleteSelectedItem = index => {
    state.selectedList.splice(index, 1)
    state.selectedRowKeys.splice(index, 1)
}

// 取消
const onCancel = () => {
    state.name = ''
    state.isSearchTable = false
    resetSelect()
    modelState.open = false
    // $ 恢复第一层数据。新的接口是每一层都会请s求的. 每次重新打开 以新状态展示
    // 恢复面包屑
    state.breadcrumbs = [state.breadcrumbs[0]]
    // 重置恢复选中的tab
    activeTabIndex.value = props.tabs.findIndex(item => item.checked) || 0
    modelState.pages = {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    }
    state.isSearch = false
    emit('cancel')
}

// 确认
const onOk = () => {
    const selectedList = JSON.parse(JSON.stringify(state.selectedList))
    emit('submit', selectedList)
    onCancel()
}

// 获取更多
const getMore = () => {
    // const options = {
    //     // 当前聚焦的tab唯一标识
    //     tabId: activeTab.value.id,
    //     index: state.breadcrumbs.length - 1,
    //     // 触发标识
    //     trigger: TRIGGER_MAP.NEXT_MORE,
    //     // name: isSearch ? state.name : '',
    // }
    const options = {
        item: state.breadcrumbs[state.breadcrumbs.length - 1],
        name: state.isSearch ? state.name : '',
    }
    emit('getMore', options, {
        pageNo: ++modelState.pages.pageNo,
        pageSize: modelState.pages.pageSize,
    })
}

// *********************
// Watch Function
// *********************

watch(
    () => props.selected,
    val => {
        state.selectedList = JSON.parse(JSON.stringify(val))
    },
)

watch(
    () => props.tabs,
    val => {
        state.breadcrumbs[0].name = val[0].tab
    },
)

defineExpose({
    modelState,
})
</script>

<style lang="less" scoped>
.mSelect-wrap {
    display: flex;
    height: 540px;
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);

    .section {
        display: flex;
        flex-direction: column;
        width: 50%;
        padding: 16px 0;

        &:last-child {
            border-left: 1px solid #f0f0f0;
        }
        &.active {
            padding-top: 0;
            .select-wrap {
                margin-top: 0;
                border-top: none;
            }
        }
    }

    .ant-input-search {
        display: block;
        width: auto;
        margin: 0 16px;
    }

    .select-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;
        border-top: 1px solid #f0f0f0;
        margin-top: 16px;
        padding: 16px 16px 0;
        overflow: hidden;
    }

    .ant-breadcrumb {
        margin-top: 16px;
        :deep(ol) {
            display: inline-block;
        }
        li {
            display: inline;
        }
        :deep(.ant-breadcrumb-link) {
            display: inline;
        }
        span:last-child {
            pointer-events: none;
        }
    }

    .tabs {
        :deep(.ant-radio-group) {
            display: flex;
            text-align: center;
        }

        :deep(.ant-radio-button-wrapper) {
            flex: 1;

            &:first-child {
                border-top-left-radius: 40px;
                border-bottom-left-radius: 40px;
            }

            &:last-child {
                border-top-right-radius: 40px;
                border-bottom-right-radius: 40px;
            }
        }
    }

    .structures {
        flex: 1;
        overflow-y: auto;
        margin-right: -10px;
        :deep(.ant-spin-nested-loading) {
            height: 100%;
        }
        :deep(.ant-spin-container) {
            height: 100%;
        }

        .row {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            padding: 0 8px;
            border-radius: 4px;
            margin-bottom: 6px;
            width: 100%;

            &:hover,
            &:active {
                background-color: #f6f6f6;
            }
        }

        .check {
            display: flex;
            align-items: center;
            padding: 6px 0;
            width: 100%;
            :deep(span:nth-of-type(2)) {
                flex: 1;
            }
        }

        .check-visible {
            pointer-events: none;

            :deep(.ant-radio),
            :deep(.ant-checkbox) {
                visibility: hidden;
            }
        }

        .cnt {
            flex: 1;
            max-width: 170px;
            margin-left: 8px;
        }

        :deep(.ant-radio + span),
        :deep(.ant-checkbox + span) {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .sub {
            color: #999;
        }

        .more {
            font-size: 14px;
            color: var(--primary-color);
            line-height: 16px;
            padding-left: 12px;
            border-left: 1px solid #d9d9d9;
            margin-left: auto;
            min-width: 30px;
            cursor: pointer;
            user-select: none;
        }
    }

    :deep(.ant-avatar) {
        font-size: 14px;
        background: var(--primary-color);
    }

    :deep(.ant-avatar-image) {
        background: transparent;
    }
}

.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
}

.data-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 90px;
    img {
        width: 201px;
        height: 117px;
    }
    p {
        font-size: 14px;
        font-weight: 400;
        color: #595959;
        line-height: 26px;
        text-align: center;
        margin-top: 17px;
    }
}
</style>
