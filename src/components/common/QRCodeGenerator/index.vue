<template>
    <canvas ref="qrcodeRef" class="code_img down_img"></canvas>
</template>

<script setup>
import { shallowRef, watch } from 'vue'
import QRCode from 'qrcode'

const props = defineProps({
    value: {
        type: String,
        default: '',
    },
})
const qrcodeRef = shallowRef()
// 初始化生成二维码
const initQrcode = () => {
    const { value } = props
    QRCode.toCanvas(qrcodeRef.value, value)
}
watch(
    () => props.value,
    (val, oldVal) => {
        if (val || oldVal) {
            initQrcode()
        }
    },
)
defineExpose({ qrcodeRef })
</script>
<style lang="less" scoped>
.code_img {
    width: 160px !important;
    height: 160px !important;
    margin: 10px auto;
    box-shadow: 0px 0px 20px 0px rgba(206, 206, 206, 0.5);
    border-radius: 14px;
}
.down_img {
    width: 160px !important;
    height: 160px !important;
    margin: 10px auto;
    border-radius: 14px;
    background: #fff;
    border: 2px solid #fdf6f6;
}
</style>
