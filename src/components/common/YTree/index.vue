<template>
    <div class="y_tree">
        <slot name="handleHandle"></slot>
        <a-tree v-if="treeData.length" :checkable="checkable" :defaultExpandAll="defaultExpandAll" v-bind="$attrs"
            :treeData="treeData" showIcon checkStrictly :selectable="true" blockNode @select="select">
            <template #title="{ dataRef }">
                <slot name="treeIcon" :treeItem="{ id: dataRef.id, name: dataRef.name }">
                    <i class="group-icon" :class="['iconfont', 'icon-yellow', treeIcon]"></i>
                </slot>
                <p class="tooltip" :title="dataRef.name">{{ dataRef.name }}</p>
                <slot name="handleItem" :key="dataRef.id" :handleItem="dataRef"></slot>
            </template>
        </a-tree>
        <slot name="handleFooter"></slot>
        <Empty v-if="!treeData.length && isShowEmpty" />
    </div>
</template>

<script setup>
// *********************
// Hooks
// *********************

const props = defineProps({
    treeIcon: {
        type: String,
        default: 'icon-icon-zu',
    },
    defaultExpandAll: {
        type: Boolean,
        default: true,
    },
    isDraggable: {
        type: Boolean,
        default: false,
    },
    isShowEmpty: {
        type: Boolean,
        default: true,
    },
    treeData: {
        type: Array,
        default: () => [],
    },
    emptyTitle: {
        type: String,
        default: '暂无数据',
    },
    checkable: {
        // 勾选框
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['emitSelect', 'update:selectedKeys'])

// *********************
// Service Function
// *********************

const select = (selectedKeys, item) => {
    if (item.node.id) {
        emit('update:selectedKeys', [item.node.id])
        emit('emitSelect', [item.node.id], item.node)
    }
}
</script>

<style scoped lang="less">
.y_tree {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 250px;
}

:deep(.ant-tree) {
    flex: 1;
    overflow-y: auto;
}

:deep(.ant-tree-list-holder-inner) {
    .ant-tree-treenode {
        width: 100%;
        padding: 5px 0;
    }

    .icon-yellow {
        color: @warning-color;
        font-size: 20px;
    }

    .ant-tree-treenode-selected {
        background-color: @acitve-background !important;

        .ant-tree-node-selected {
            color: var(--primary-color);

            .icon-yellow {
                color: var(--primary-color);
            }

            .handle_icon {
                display: inline-block;
            }
        }
    }

    .ant-tree-node-content-wrapper {
        background-color: transparent;
        display: flex;
        flex: 1;
    }
}

:deep(.ant-tree-title) {
    display: flex;
    overflow: hidden;

    .tooltip {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>
