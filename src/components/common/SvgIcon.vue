<template>
    <svg class="icon" :width="`${size}px`" :height="`${size}px`">
        <use :xlink:href="`#${name}`" :fill="color"></use>
    </svg>
</template>
<script setup>
defineProps({
    name: {
        type: String,
        default: '',
    },
    size: {
        type: String,
        default: '16',
    },
    color: {
        type: String,
        default: '#2C2C2C',
    },
})
</script>
<style lang="less" scoped>
.icon {
    fill: currentColor;
    overflow: hidden;
}
</style>
