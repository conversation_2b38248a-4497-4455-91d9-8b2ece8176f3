<template>
    <div class="empty" :style="emptyStyle">
        <img :src="empty" alt="" srcset="" />
        <p class="tips">{{ tips }}</p>
    </div>
</template>

<script setup>
import empty from '@/assets/images/empty.png'
const props = defineProps({
    tips: {
        type: String,
        default: '暂无数据',
    },
    // 自定义图片样式
    emptyStyle: {
        type: Object,
        default: () => {
            return {
                width: '200px',
                height: '180px',
            }
        },
    }
})
</script>

<style scoped lang="less">
.empty {
    text-align: center;
    margin: 0 auto;

    img {
        width: 200px;
        height: 180px;
    }

    .tips {
        color: #bdbcbc;
    }
}
</style>
