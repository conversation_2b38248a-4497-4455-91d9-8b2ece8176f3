<template>
    <Editor v-model="content" :init="init" tag-name="div" />
</template>
<script setup>
import tinymce from 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver/theme' // 引用主题文件
import 'tinymce/icons/default' // 引用图标文件
import 'tinymce/models/dom'
// tinymce插件可按自己的需要进行导入
// 更多插件参考：https://www.tiny.cloud/docs/plugins/
import 'tinymce/plugins/advlist'
import 'tinymce/plugins/anchor'
import 'tinymce/plugins/autolink'
import 'tinymce/plugins/autoresize'
import 'tinymce/plugins/autosave'
import 'tinymce/plugins/charmap' // 特殊字符
import 'tinymce/plugins/code' // 查看源码
import 'tinymce/plugins/codesample' // 插入代码
import 'tinymce/plugins/directionality'
import 'tinymce/plugins/emoticons'
import 'tinymce/plugins/fullscreen' //全屏
// import 'tinymce/plugins/help'
import 'tinymce/plugins/image' // 插入上传图片插件
import 'tinymce/plugins/importcss' //图片工具
import 'tinymce/plugins/insertdatetime' //时间插入
import 'tinymce/plugins/link'
import 'tinymce/plugins/lists' // 列表插件
// import 'tinymce/plugins/media' // 插入视频插件
import 'tinymce/plugins/nonbreaking'
import 'tinymce/plugins/pagebreak' //分页
import 'tinymce/plugins/preview' // 预览
import 'tinymce/plugins/quickbars'
import 'tinymce/plugins/save' // 保存
import 'tinymce/plugins/searchreplace' //查询替换
import 'tinymce/plugins/table' // 插入表格插件
// import 'tinymce/plugins/template' //插入模板
import 'tinymce/plugins/visualblocks'
import 'tinymce/plugins/visualchars'
import 'tinymce/plugins/wordcount' // 字数统计插件
// import 'tinymce/plugins/fontsizeselect'

// v-model
const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    // 上传文件 类型
    folderType: {
        type: String,
        default: '',
    },
})
const emit = defineEmits(['update:modelValue'])
// 配置
const init = {
    language_url: '/tinymce/langs/zh-Hans.js', // 中文语言包路径
    language: 'zh-Hans',
    skin_url: '/tinymce/skins/ui/oxide', // 编辑器皮肤样式
    content_css: '/tinymce/skins/content/default/content.min.css',
    menubar: false, // 隐藏菜单栏
    autoresize_bottom_margin: 50,
    max_height: 500,
    min_height: 350,
    toolbar_sticky: false,
    file_picker_types: 'media',
    plugins:
        'wordcount visualchars  visualblocks  searchreplace save preview pagebreak nonbreaking  insertdatetime importcss image fullscreen directionality codesample code charmap code  anchor autolink autoresize autosave',
    toolbar:
        //  outdent indent
        'undo redo aligncenter alignleft alignright alignjustify lineheight underline removeformat forecolor backcolor bold italic image  fontsize fontfamily fullscreen',
    content_style: 'p {margin: 0px; border:0px; padding: 0px;}',
    font_size_formats: '14px 16px 18px 24px 36px 48px 56px 72px',
    font_family_formats:
        '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方= PingFang SC, Microsoft YaHei, sans- serif; 宋体 = simsun, serif; 仿宋体 = FangSong, serif; 黑体 = SimHei, sans - serif; Arial = arial, helvetica, sans - serif;Arial Black = arial black, avant garde;Book Antiqua = book antiqua, palatino; ',
    branding: false,
    elementpath: false,
    resize: false, // 禁止改变大小
    statusbar: false, // 隐藏底部状态栏
    contextmenu_never_use_native: false,
    // 微信小程序 只有这里配置的节点和属性，编辑器才支持，不符合的会自动过滤(下面是目前rich-text支持的所有节点和属性)
    valid_elements:
        'p[style|class],a[style|class],abbr[style|class],b[style|class],blockquote[style|class],br[style|class],code[style|class],col[style|class|span|width],colgroup[style|class|span|width],dd[style|class],del[style|class],div[style|class],dl[style|class],dt[style|class],em[style|class],fieldset[style|class],h1[style|class],h2[style|class],h3[style|class],h4[style|class],h5[style|class],h6[style|class],hr[style|class],i[style|class],img[style|class|alt|src|height|width],ins[style|class],label[style|class],legend[style|class],li[style|class],ol[style|class|start|type],p[style|class],q[style|class],span[style|class],strong[style|class],sub[style|class],sup[style|class],table[style|class|width],tbody[style|class],td[style|class|colspan|height|rowspan|width],tfoot[style|class],th[style|class|colspan|height|rowspan|width],thead[style|class],tr[style|class],ul[style|class]',
    // 如果配置了这个，编辑器会自动抹掉节点的属性(如果要保留请设置为false)
    paste_as_text: true,
    // rich-text不支持html实体，所以我们得将实体转为字符
    entities:
        'nbsp,160,iexcl,161,cent,162,pound,163,curren,164,yen,165,brvbar,166,sect,167,uml,168,copy,169,ordf,170,' +
        'laquo,171,not,172,shy,173,reg,174,macr,175,deg,176,plusmn,177,sup2,178,sup3,179,acute,180,micro,181,para,182,' +
        'middot,183,cedil,184,sup1,185,ordm,186,raquo,187,frac14,188,frac12,189,frac34,190,iquest,191,Agrave,192,Aacute,193,' +
        'Acirc,194,Atilde,195,Auml,196,Aring,197,AElig,198,Ccedil,199,Egrave,200,Eacute,201,Ecirc,202,Euml,203,Igrave,204,' +
        'Iacute,205,Icirc,206,Iuml,207,ETH,208,Ntilde,209,Ograve,210,Oacute,211,Ocirc,212,Otilde,213,Ouml,214,times,215,' +
        'Oslash,216,Ugrave,217,Uacute,218,Ucirc,219,Uuml,220,Yacute,221,THORN,222,szlig,223,agrave,224,aacute,225,acirc,226,' +
        'atilde,227,auml,228,aring,229,aelig,230,ccedil,231,egrave,232,eacute,233,ecirc,234,euml,235,igrave,236,iacute,237,' +
        'icirc,238,iuml,239,eth,240,ntilde,241,ograve,242,oacute,243,ocirc,244,otilde,245,ouml,246,divide,247,oslash,248,' +
        'ugrave,249,uacute,250,ucirc,251,uuml,252,yacute,253,thorn,254,yuml,255,fnof,402,Alpha,913,Beta,914,Gamma,915,Delta,916,' +
        'Epsilon,917,Zeta,918,Eta,919,Theta,920,Iota,921,Kappa,922,Lambda,923,Mu,924,Nu,925,Xi,926,Omicron,927,Pi,928,Rho,929,' +
        'Sigma,931,Tau,932,Upsilon,933,Phi,934,Chi,935,Psi,936,Omega,937,alpha,945,beta,946,gamma,947,delta,948,epsilon,949,' +
        'zeta,950,eta,951,theta,952,iota,953,kappa,954,lambda,955,mu,956,nu,957,xi,958,omicron,959,pi,960,rho,961,sigmaf,962,' +
        'sigma,963,tau,964,upsilon,965,phi,966,chi,967,psi,968,omega,969,thetasym,977,upsih,978,piv,982,bull,8226,hellip,8230,' +
        'prime,8242,Prime,8243,oline,8254,frasl,8260,weierp,8472,image,8465,real,8476,trade,8482,alefsym,8501,larr,8592,uarr,8593,' +
        'rarr,8594,darr,8595,harr,8596,crarr,8629,lArr,8656,uArr,8657,rArr,8658,dArr,8659,hArr,8660,forall,8704,part,8706,exist,8707,' +
        'empty,8709,nabla,8711,isin,8712,notin,8713,ni,8715,prod,8719,sum,8721,minus,8722,lowast,8727,radic,8730,prop,8733,infin,8734,' +
        'ang,8736,and,8743,or,8744,cap,8745,cup,8746,int,8747,there4,8756,sim,8764,cong,8773,asymp,8776,ne,8800,equiv,8801,le,8804,ge,8805,' +
        'sub,8834,sup,8835,nsub,8836,sube,8838,supe,8839,oplus,8853,otimes,8855,perp,8869,sdot,8901,lceil,8968,rceil,8969,lfloor,8970,' +
        'rfloor,8971,lang,9001,rang,9002,loz,9674,spades,9824,clubs,9827,hearts,9829,diams,9830,OElig,338,oelig,339,Scaron,352,scaron,353,' +
        'Yuml,376,circ,710,tilde,732,ensp,8194,emsp,8195,thinsp,8201,zwnj,8204,zwj,8205,lrm,8206,rlm,8207,ndash,8211,mdash,8212,lsquo,8216,' +
        'rsquo,8217,sbquo,8218,ldquo,8220,rdquo,8221,bdquo,8222,dagger,8224,Dagger,8225,permil,8240,lsaquo,8249,rsaquo,8250,euro,8364,',
    urlconverter_callback: (url, node, onSave, name) => {
        if (node === 'img' && url.startsWith('blob:')) {
            tinymce.activeEditor && tinymce.activeEditor.uploadImages()
        }
        return url
    },
    images_upload_handler: (blobInfo, progress) =>
        new Promise((resolve, reject) => {
            progress(0)
            // blobInfo.blob() 得到图片的file对象
            let file = blobInfo.blob()
            if (file.size > 1024 * 1024 * 10) {
                YMessage.error('图片大小不能超过10M')
                return
            }
            http.form('/api/common/upload/upload', { file, type: props.folderType }, {})
                .then(res => {
                    progress(100)
                    const url = res.data.url
                    resolve(url)
                    return
                })
                .catch(err => {
                    reject('上传失败')
                    return
                })
        }),
    file_picker_callback: async (callback, value, meta) => {
        if (meta.filetype === 'media') {
            let input = document.createElement('input')
            input.type = 'file'
            input.onchange = async function () {
                let file = this.files[0]
                http.form('/api/common/upload/upload', { file, type: props.folderType }, {}).then(({ data }) => {
                    callback(data?.url)
                })
            }
            input.click()
        }
    },
}
tinymce.init // 初始化
const content = ref(props.modelValue)
watch(props, newVal => (content.value = newVal.modelValue))
watch(content, newVal => emit('update:modelValue', newVal))
</script>
<style scoped>
.tox-tinymce-aux {
    z-index: 9999 !important;
}
</style>
