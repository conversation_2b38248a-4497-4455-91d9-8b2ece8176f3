/*
 * @Descripttion:导出
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-06-25 15:15:35
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-09 15:59:28
 */
import html2canvas from 'html2canvas'

export const dataURLtoFile = (dataurl, filename) => {
    const arr = dataurl.split(',')
    const mime = arr[0].match(/:(.*?);/)[1]
    const bstr = atob(arr[1])
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    return new File([u8arr], filename, { type: mime })
}

export const generatorImage = (targetDom, name, fileType = 'file') => {
    return new Promise((resolve, reject) => {
        window.scrollTo(0, 0)
        html2canvas(targetDom, {
            logging: true,
            scale: 1,
            allowTaint: false,
            useCORS: true,
            height: targetDom?.offsetHeight || 0,
            width: targetDom?.offsetWidth || 0,
            windowWidth: document.body.scrollWidth,
            windowHeight: document.body.scrollHeight,
            dpi: window.devicePixelRatio,
            backgroundColor: null
        })
            .then((canvas) => {
                const base64 = canvas.toDataURL('image/png', 1)
                const file = dataURLtoFile(base64, name)
                resolve(fileType === 'file' ? file : base64)
            })
            .catch((err) => {
                reject(err)
            })
    })
}
