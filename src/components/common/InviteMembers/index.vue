<!-- InviteMembers 邀请成员-->
<template>
    <a-modal
        v-model:visible="props.modalVisible"
        :title="title"
        footer=""
        :width="560"
        class="invite_members"
        :bodyStyle="{ padding: '24px' }"
        @cancel="handleCancel"
        @ok="setModal1Visible(false)"
    >
        <a-spin :spinning="state.loading">
            <div class="footer mb-10">
                <label class="lifespan">设置邀请有效期：</label>
                <a-select
                    mt-5
                    v-model:value="state.lifespan"
                    style="width: 100%"
                    placeholder="请选择有效期"
                    @select="handleSelect"
                >
                    <a-select-option v-for="item in state.lifespans" :key="item.key" :value="item.key">
                        {{ item.value }}
                    </a-select-option>
                </a-select>
            </div>
            <div class="head">分享二维码或链接，邀请填写学生信息，加入绑定，即可通话</div>
            <div class="title">
                {{ state.inviteForm.schoolName }}
            </div>
            <div class="content">
                <!-- 下载的dom -->
                <div class="codebox deviation" ref="codebox">
                    <div class="title">{{ state.inviteForm.schoolName }}</div>
                    <div class="top_text">扫码填写学生信息，加入绑定，即可通话</div>
                    <QRCodeGenerator ref="qrcodeRef" :value="state.inviteForm.url" />
                    <div class="color-#00B781 text-center">二维码{{ state.lifespan }}天有效</div>
                </div>
                <div class="codebox">
                    <QRCodeGenerator ref="qrcodeRef" :value="state.inviteForm.url" />
                </div>
                <div class="btns">
                    <a-button type="link" @click="downloadQRCode">下载二维码</a-button>
                    <a-button type="link" @click="handCopyLink">复制链接</a-button>
                </div>
            </div>
        </a-spin>
    </a-modal>
</template>

<script setup>
import { reactive, watch, shallowRef } from 'vue'
import QRCodeGenerator from '../QRCodeGenerator/index.vue'
import { copyLink } from '@/utils/common.js'
import Http from '@/utils/http.js'
import { generatorImage } from './exportCanvas.js'
import { message } from 'ant-design-vue'
const codebox = shallowRef()
const qrcodeRef = shallowRef()
const props = defineProps({
    modalVisible: {
        type: Boolean,
        default: false,
    },
    deptId: {
        type: String,
        default: 'false',
    },
    getUrl: {
        type: String,
        default: '',
    },
    title: {
        type: String,
        default: '邀请填写',
    },
})
const emit = defineEmits(['update:modalVisible'])
const state = reactive({
    lifespan: 7,
    lifespans: [
        { key: 3, value: '3天' },
        { key: 7, value: '7天' },
        { key: 30, value: '30天' },
        { key: 90, value: '90天' },
    ],
    inviteForm: {
        url: '',
        schoolName: '',
    },
    loading: false,
})
const handleSelect = event => {
    updateEmployeeCutTime()
}
const handleCancel = () => {
    emit('update:modalVisible', false)
}
const setModal1Visible = () => {
    handleCancel()
}

// 下载二维码
const downloadQRCode = async () => {
    const name = `邀请你加入${state.inviteForm.schoolName}.png`
    const base64 = await generatorImage(codebox.value, name, 'base64')
    const a = document.createElement('a')
    a.href = base64
    a.setAttribute('download', name)
    a.click()
}
// 复制链接
const handCopyLink = () => {
    copyLink(state.inviteForm.url)
    message.success('已复制链接')
}
// 邀请切换过期时间
const updateEmployeeCutTime = () => {
    getEmployeeInvite()
}

const getEmployeeInvite = () => {
    const params = {
        day: state.lifespan,
    }
    state.loading = true
    Http.post('/api/school/student/invite', params)
        .then(({ data }) => {
            const { inviteUrl, schoolName } = data
            state.inviteForm.url = inviteUrl
            state.inviteForm.schoolName = schoolName
        })
        .finally(() => (state.loading = false))
}
watch(
    () => props.modalVisible,
    val => {
        if (val) {
            state.lifespan = 7
            getEmployeeInvite()
        }
    },
)
</script>

<style scoped lang="less">
.invite_members {
    .head {
        color: #000000a6;
        text-align: center;
    }
    .title {
        padding-top: 10px;
        text-align: center;
        font-weight: 600;
        font-size: 18px;
    }
    .content {
        width: 266px;
        margin: 15px auto;
        .codebox {
            width: 100%;
            height: 180px;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-image: url('/image/inviteBg.png');
            background-size: cover;
            text-align: center;
            border-radius: 6px;
            // box-shadow: 0px 0px 15px 0px #d4f9ee;
            .top_text {
                text-align: center;
                margin-top: 7px;
            }
            .user_name {
                color: @suggestive-color;
            }
        }
        .deviation {
            position: fixed;
            left: -1000%;
            width: 300px;
            height: 290px;
            background: #fff;
            .code_img {
                box-shadow: none;
            }
        }
        .btns {
            margin: 15px auto;
            text-align: center;
            .ant-btn-link {
                color: var(--primary-color);
            }
        }
    }
    .lifespan {
        color: @suggestive-color;
    }
}
</style>
