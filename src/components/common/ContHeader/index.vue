<template>
    <div class="container_box" :class="{ meunClass: type === 'menu', noAuth }">
        <div v-if="type === 'back' && hasBack" flex flex-items-center>
            <svg-icon name="icon-icon-back" size="24" mr-12 @click="back" style="cursor: pointer"></svg-icon>
            <div font-size-14 font-600 flex-1>
                <slot>{{ title || route.meta.title }}</slot>
            </div>
            <slot name="rightExtra"></slot>
        </div>
        <div v-else-if="type === 'menu'">
            <a-tabs :activeKey="aKey" @change="change" class="yd_tabs">
                <a-tab-pane v-for="item in list" :key="item.value" :tab="item.name"></a-tab-pane>
                <template #rightExtra>
                    <slot name="rightExtra"></slot>
                </template>
            </a-tabs>
            <NoAuth v-if="!aKey"></NoAuth>
        </div>
        <div v-else font-size-16 font-600 flex flex-justify-between flex-items-center>
            <slot>{{ title || route.meta.title }}</slot>
            <slot name="rightExtra"></slot>
        </div>
    </div>
</template>
<script setup>
import { computed } from 'vue'
const store = useStore()
const tabStore = useTabStore()
const router = useRouter()

// *********************
// Hooks Function
// *********************
const props = defineProps({
    type: {
        type: String,
        default: 'default',
    },
    activeKey: {
        type: [String, Number],
        default: 1,
    },
    menuList: {
        type: Array,
        default: [],
    },
    title: {
        type: String,
        default: '',
    },
    goBack: {
        type: Function,
        required: false,
    },
    // !!! 唯一标识（每个页面值必须唯一）
    hash: {
        type: String,
        default: '',
    },
})
const aKey = ref('')
const route = useRoute()
const emit = defineEmits(['update:activeKey', 'change'])
const list = ref([])

const noAuth = computed(() => {
    return !aKey.value && props.type === 'menu'
})

const hash = computed(() => {
    const hash = window.location.hash.replace(/\?.*/, '')
    return hash + isEmptyStr(props.hash)
})

const hasBack = computed(() => {
    return router.options.history.state.back !== null
})

// *********************
// Service Function
// *********************

const isEmptyStr = i => {
    return i || ''
}

const init = () => {
    const value = tabStore.get(hash.value)
    if (value) {
        emit('update:activeKey', value)
        emit('change', value)
    }
}
const back = () => {
    props.goBack ? props.goBack() : router.back()
}

const change = val => {
    aKey.value = val
    emit('update:activeKey', val)
    emit('change', val)
    tabStore.set(hash.value, val)
}

// *********************
// Life Event Function
// *********************

init()

// *********************
// Watch Function
// *********************

watch(
    () => props.menuList,
    menuList => {
        menuList.forEach(i => {
            if (i.auth) {
                if (store.Perms.includes(i.auth)) {
                    list.value.push(i)
                }
            } else {
                list.value.push(i)
            }
        })
        aKey.value = list.value[0]?.value
        emit('change', aKey.value)
    },
    {
        immediate: true,
    },
)

watch(
    () => props.activeKey,
    activeKey => {
        // $ 外层触发的activeKey可能没有权限，显示无权限页面
        const hasAuth = list.value.some(i => i.value === activeKey)
        const val = hasAuth ? activeKey : undefined
        change(val)
    },
)
</script>
<style lang="less" scoped>
.container_box {
    padding: 12px;
    border-bottom: 1px solid #d9d9d9;
    .yd_tabs {
        line-height: 38px;
        :deep(.ant-tabs-nav) {
            margin: 0;
            &::before {
                border-bottom: none;
            }
        }
        :deep(.ant-tabs-tab) {
            font-weight: 600;
            padding: 4px 0;
        }
    }
}
.noAuth {
    border-bottom: none;
}
.meunClass {
    padding-top: 0px;
    padding-bottom: 0px;
}
</style>
