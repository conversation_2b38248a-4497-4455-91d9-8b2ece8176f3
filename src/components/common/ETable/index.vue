<template>
    <a-table rowKey="id" v-bind="$attrs" :pagination="pagination">
        <template #emptyText>
            <slot name="emptyText">
                <Empty :emptyStyle="{ marginBottom: '65px' }"></Empty>
            </slot>
        </template>
        <template #headerCell="{ column }">
            <Tooltip v-if="slotsName" :maxWidth="column.width" :title="column.title"></Tooltip>
            <template v-for="item in Object.keys(slots)" :key="item">
                <template v-if="['headerCell'].includes(item)">
                    <slot :name="item" :column />
                </template>
            </template>
        </template>

        <template #bodyCell="{ column, text, record, index }">
            <Tooltip v-if="slotsName" :maxWidth="column.width" :title="text"></Tooltip>
            <template v-for="item in Object.keys(slots)" :key="item">
                <template v-if="['bodyCell'].includes(item)">
                    <slot :name="item" :column :text :record :index />
                </template>
            </template>
        </template>
    </a-table>
</template>
<script setup>
const props = defineProps({
    paginations: {
        type: Object,
        default: () => {
            return {
                total: 0,
                pageNo: 1,
                pageSize: 10,
            }
        },
    },
})
const pagination = ref({
    hideOnSinglePage: true,
    showQuickJumper: true,
    showLessItems: true,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '30', '40', '100'],
    showTotal: (total, range) => `共 ${total} 条`,
})
watch(
    () => props.paginations,
    val => {
        pagination.value = {
            ...pagination.value,
            ...val,
            current: val.pageNo,
        }
        pagination.value.hideOnSinglePage = val.total <= 10
    },
    {
        deep: true,
        immediate: true,
    },
)
const slots = useSlots()
// 判断是否有插槽
const slotsName = computed(() => {
    return Object.keys(slots).length == 1 && Object.keys(slots)[0] == 'default'
})
</script>
<style lang="less" scoped>
:deep(.ant-pagination) {
    .ant-pagination-next,
    .ant-pagination-prev,
    .ant-pagination-item {
        border: 1px solid @border-color-base;

        &.ant-pagination-item-active {
            border-color: @primary-color;
        }
    }
}
</style>
