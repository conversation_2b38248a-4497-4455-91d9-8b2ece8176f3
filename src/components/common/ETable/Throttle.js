export default class {
    timer = null
    date = 0
    start(f, arg, wait) {
        const t = +new Date()
        if (this.timer) {
            clearTimeout(this.timer)
            this.timer = null
        }
        if (t - this.date > wait) {
            f(arg)
        } else {
            this.timer = setTimeout(() => {
                f(arg)
            }, wait)
        }
        this.date = t
    }
}
