<template>
    <a-modal v-model:open="state.open" :maskClosable="false" :width="408" :title="title">
        <article v-scrollTo>
            <ul class="tags-list">
                <li v-for="item in state.list" :key="item.id">
                    <div class="title">
                        <span class="require-symbol" v-if="item.required" mr-2>*</span>
                        <span class="label-dot" :style="{ background: item.colorRgb }"></span>
                        <span class="name">
                            {{ item.name }}
                        </span>
                    </div>
                    <span
                        class="tag"
                        v-for="tag in item.symbols"
                        :key="tag.id"
                        :style="tagStyle(item.colorRgb, tag.id)"
                        @click="updateTags(item, tag.id)"
                    >
                        {{ tag.name }}
                    </span>
                </li>
            </ul>
        </article>
        <template #footer>
            <footer>
                <a-button type="link" class="btn-link-color" @click="reset">清除已选择的标签</a-button>
                <div class="control-btn">
                    <a-button @click="cancel">取 消</a-button>
                    <a-button type="primary" @click="confirm">确 定</a-button>
                </div>
            </footer>
        </template>
    </a-modal>
</template>

<script setup>
// *********************
// Hooks Function
// *********************
const emit = defineEmits(['confirm'])
const props = defineProps({
    title: {
        type: String,
        default: '',
    },
})

const state = reactive({
    open: false,
    list: [],
    value: new Set(),
})

const tagStyle = computed(() => {
    return (color, id) => {
        if (state.value.has(id)) {
            const result = color.match(/\(([^)]+)\)/)?.[1] || '0,183,129'
            return {
                color,
                background: `rgba(${result}, 0.08)`,
                borderColor: color,
            }
        } else {
            return {
                color: '#595959',
                background: '#FFFFFF',
                borderColor: '#D9D9D9',
            }
        }
    }
})
// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const updateTags = (item, id) => {
    // 判断当前标签组是否单选
    const { allowMultipleCheck, symbols } = item
    if (!allowMultipleCheck) {
        // 当前标签组只能单选
        const ids = symbols.map(item => item.id)
        // 判断是否存在了当前
        const sameGroupId = ids.find(id => state.value.has(id)) || ''
        if (sameGroupId) {
            // 标签切换
            state.value.delete(sameGroupId)
            if (sameGroupId !== id) {
                state.value.add(id)
            }
            return
        }
    }

    if (state.value.has(id)) {
        state.value.delete(id)
    } else {
        state.value.add(id)
    }
}

const reset = () => {
    state.value.clear()
}

const cancel = () => {
    state.open = false
}

const confirm = () => {
    // 判断是否所有必填都勾选了
    let illegalName = ''
    const isValid = state.list.every(item => {
        const { required, symbols, name } = item
        if (required) {
            const flag = symbols.some(i => state.value.has(i.id))
            if (!flag) {
                illegalName = name
            }
            return flag
        } else {
            return true
        }
    })

    if (!isValid) {
        YMessage.error(`${illegalName} 必填`)
        return
    }
    const value = Array.from(state.value)
    state.open = false
    emit('confirm', value)
}

// *********************
// DefineExpose Function
// *********************

const showModal = record => {
    state.list = record.list
    state.value = new Set(record.value)
    state.open = true
}

defineExpose({ showModal })
</script>

<style lang="less" scoped>
article {
    overflow-y: auto;
    max-height: 606px;
    padding: 0 24px 24px 24px;
}

.tags-list {
    .title {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        margin-top: 24px;
        .require-symbol {
            box-sizing: border-box;
            color: #ff4d4f;
            padding-top: 3px;
        }
        .label-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 8px;
            background: #4380ed;
            margin: -1px 4px 0 4px;
        }
    }
    .tag {
        display: inline-block;
        padding: 3px 7px;
        font-size: 14px;
        font-weight: 400;
        color: #595959;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        margin: 16px 12px 0 0;
        cursor: pointer;
    }
}

footer {
    display: flex;
    width: 100%;
    justify-content: space-between;
}
</style>
