<template>
    <div class="filter-criteria">
        <p class="title">筛选条件</p>
        <div class="condition-list-container">
            <ul class="condition-list">
                <li class="condition-item" v-for="(item, index) in list" :key="index">
                    <p>{{ item.label }}</p>
                    <p>{{ item.tag }}</p>
                    <p>{{ item.value }}</p>
                    <img src="@/assets/images/delete.png" alt="delete" @click="onDelete(item)" />
                </li>
            </ul>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    list: {
        type: Array,
        default: [],
    },
})

const emit = defineEmits(['delete'])

// *********************
// Service Function
// *********************

const onDelete = item => {
    emit('delete', item)
}
</script>

<style lang="less" scoped>
.filter-criteria {
    display: flex;
    align-items: center;
    height: 36px;
    background: #f3f3f3;
    border-radius: 4px;
    margin-top: 16px;
    padding: 0 8px;
    .title {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
        line-height: 20px;
    }

    .condition-list-container {
        flex: 1;
        overflow: auto hidden;
    }

    .condition-list {
        display: flex;
        align-items: center;
        .condition-item {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            height: 28px;
            background: #e8e8e8;
            border-radius: 4px;
            margin-left: 12px;
            padding-left: 8px;
            cursor: default;
            &:hover {
                color: #19be8d;
                background: rgba(25, 190, 141, 0.1);
            }
            p {
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                &:nth-of-type(2) {
                    margin: 0 8px;
                }
            }
            img {
                width: 20px;
                cursor: pointer;
                margin-left: 8px;
            }
        }
    }
}
</style>
