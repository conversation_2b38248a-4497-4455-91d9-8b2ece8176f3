<template>
    <div class="grid_layout">
        <div class="contner" mb-0>
            <template v-if="state.fileList.length >= limit">
                <div class="list-item" v-for="item in state.fileList" :key="item.url">
                    <div class="mask" @mouseenter="state.hoverUrl = item.url" @mouseleave="state.hoverUrl = null">
                        <div class="btn" v-show="state.hoverUrl === item.url">
                            <EyeOutlined class="icon" @click="preview(item.url)" />
                            <DeleteOutlined v-if="!disabled" class="icon" ml-10 @click="remove(item.url)" />
                        </div>
                    </div>
                    <img :src="item.url" alt="" />
                </div>
            </template>
            <a-upload v-else class="reset-upload" :file-list="[]" :disabled="disabled" list-type="picture-card"
                action="/" :before-upload="beforeUpload">
                <div>
                    <loading-outlined v-if="state.loading"></loading-outlined>
                    <template v-else>
                        <plus-outlined></plus-outlined>
                        <div style="margin-top: 8px">{{ text }}</div>
                    </template>
                </div>
            </a-upload>
            <p class="tips">{{ tips }}</p>
        </div>
        <a-image :style="{ display: 'none' }" :preview="{
            visible: state.previewVisible,
            onVisibleChange: openPreview,
        }" :src="state.previewImage" />
    </div>
</template>

<script setup>
import { PlusOutlined } from '@ant-design/icons-vue'

// *********************
// Hooks Function
// *********************

const props = defineProps({
    // 提示文字
    text: {
        type: String,
        default: '点击上传',
    },
    maxCount: {
        type: Number,
        default: 1,
    },
    // 图片大小 单位MB
    sizeNum: {
        type: Number,
        default: 5,
    },
    // 最大上传数量
    limit: {
        type: Number,
        default: 1,
    },
    // 上传类型
    accept: {
        type: String,
        default: '',
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false,
    },
    // 上传模块 云平台业务上传
    folderType: {
        type: String,
        default: 'alarm',
    },
    tips: {
        type: String,
        default: '',
    },
    setLoading: {
        type: Boolean,
        default: false,
    },
    // 回显的文件列表
    feedbackFileList: {
        type: Array,
        // 里面有 {url:'',name:''}
        default: () => [],
    },
})

const emit = defineEmits(['emitFileList', 'update:setLoading'])
const state = reactive({
    fileList: [],
    previewVisible: false,
    previewImage: '',
    loading: false,
    hoverUrl: null,
})

const openPreview = flag => {
    state.previewVisible = flag
}

const preview = async item => {
    state.previewImage = item
    openPreview(true)
}
// 删除图片
const remove = item => {
    const _fileList = state.fileList.filter(v => v.url !== item)
    state.fileList.length = _fileList
    emit('emitFileList', state.fileList)
}
// 上传图片
const uploadImages = async file => {
    try {
        const { data } = await http.form('/file/cloud/common/upload', { file, folderType: props.folderType })
        data[0].size = file.size
        const fileList = [data[0]]
        state.fileList = fileList
        emit('emitFileList', fileList)
    } catch (error) {
        console.log('error: ', error)
        YMessage.error('上传失败!')
    } finally {
        state.loading = false
    }
}

// 判断图片大小以及转成base64
const beforeUpload = file => {
    state.loading = true
    // 图片数量
    if (state.fileList.length > props.limit) {
        YMessage.error(`最多只能上传${props.limit}个文件!`)
        state.loading = false
        return false
    }
    const isJpgOrPng = ['image/jpeg', 'image/webp', 'image/jpg', 'image/png']
    if (!isJpgOrPng.includes(file.type)) {
        state.loading = true
        YMessage.error('只能上传jpg，png格式!')
        return false
    }

    // file.size 文件大小
    const isLt = file.size / 1024 / 1024 < props.sizeNum
    if (!isLt) {
        YMessage.error(`文件大小不能超过${props.sizeNum}M!`)
        state.loading = false
        return false
    }
    uploadImages(file)
    return false
}
// 监听loading
watch(
    () => state.loading,
    val => {
        emit('update:setLoading', val)
    },
)
// 监听回显的文件列表
watch(
    () => props.feedbackFileList,
    v => {
        state.fileList = v
    },
    { deep: true },
)
</script>

<style lang="less" scoped>
.grid_layout {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .contner {
        display: flex;
        align-items: center;

        .reset-upload {
            margin-bottom: 10px;
            text-align: center;
            width: auto;

            .ant-upload-select {
                width: 150px;
                height: 100px;
                border-radius: 12px;
            }
        }

        .tips {
            color: #999999;
            padding-left: 10px;
        }

        .list-item {
            width: 90px;
            height: 92px;
            position: relative;
            border-radius: 6px;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
            }

            .mask {
                display: flex;
                align-items: center;
                opacity: 0.7;
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;

                .btn {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    background-color: @heading-color;

                    .icon {
                        font-size: 15px;
                        color: rgba(255, 255, 255, 0.65);
                    }

                    .anticon-eye:hover,
                    .anticon-delete:hover {
                        color: @body-background;
                    }
                }
            }
        }
    }
}
</style>
