<template>
    <div class="login_box" :style="{ background: introduction[store.sysIdent].bgColor }">
        <div class="introduction">
            <img :src="getStaticFile(introduction[store.sysIdent].img)" alt="logo" mr-30 />
        </div>
        <div class="form_box" :style="{ boxShadow: `0px 7px 16px 0px ${introduction[store.sysIdent].shadowColor}` }">
            <p class="form_title" v-if="state.showArea !== SHOW_AREA_MAP.PASSWORD">
                {{ '欢迎登录智慧点餐系统' }}
            </p>
            <!-- 登录 -->
            <LoginContent
                v-if="state.showArea === SHOW_AREA_MAP.LOGIN"
                :loading="state.loadingBtn"
                @submit="submitLogin"
                :loginText="state.loginText"
                :isScanCode="state.isScanCode"
            />
            <!-- 重置密码 -->
            <Password v-else-if="state.showArea === SHOW_AREA_MAP.PASSWORD" @submit="submitLogin" />
        </div>
    </div>
</template>

<script setup>
import LoginContent from './loginContent.vue'
import RSA from './rsa.js'
import { getStaticFile } from '@/utils/common.js'
const store = useStore()

// 显示区域类型
const SHOW_AREA_MAP = {
    LOGIN: 'login',
    SCHOOL: 'school',
    PASSWORD: 'password',
}

const state = reactive({
    setPassword: {
        isAdmin: false,
        isTips: 'set',
        resetTitle: '设置新密码',
        userPhone: '',
    },
    isScanCode: false, // 是否为扫码登录
    showArea: SHOW_AREA_MAP.LOGIN, // 界面显示的区域
    isLogin: false, // 是否已经登录（登录过后选学校）
    loginText: '登 录', // 按钮文案
    loadingBtn: false, // 按钮加载状态
    schoolList: [], //学校列表
    accessToken: null, // token
})

// 介绍信息
const introduction = {
    user: {
        img: 'pic1.png',
        bgColor: '#fafefc',
        shadowColor: '#D5ECE4',
        loginTitle: '欢迎登录用户端',
    },
    agent: {
        img: 'pic3.png',
        bgColor: '#f9fbff',
        shadowColor: '#E0EEFF',
        loginTitle: '欢迎登录代理商端',
    },
    management: {
        img: 'pic2.png',
        bgColor: '#f9fbff',
        shadowColor: '#E0EEFF',
        loginTitle: '欢迎登录管理端 ',
    },
}

// 登录状态更新
const updateLoginStatus = status => {
    switch (status) {
        case 'success': {
            state.loginText = '登录'
            state.loadingBtn = false
            break
        }
        case 'error': {
            state.loginText = '登录失败'
            state.loadingBtn = false
            store.clearUser()
            setTimeout(() => {
                state.loginText = '登录'
            }, 2000)
            break
        }
        case 'loading': {
            state.loginText = '登录中...'
            state.loadingBtn = true
            break
        }
    }
}

// 分三个端 user:用户端  agent:代理商端  management:管理端
const platform = {
    user: 'school',
    agent: 'partner',
    management: 'admin',
}

// 获取用户信息
const getCurrentUser = async () => {
    try {
        // 获取用户信息之前先获取一下当前用户状态
        const res = await http.post(`/api/${platform[store.sysIdent]}/me/status`)
        if (res.data.password.status == 'CHANGE_REQUIRED') {
            // 首次登录跳转重设密码
            state.showArea = SHOW_AREA_MAP.PASSWORD
            return
        }
        // 获取用户信息
        const { data } = await http.post(`/api/${platform[store.sysIdent]}/me`)
        store.userInfo = data

        window.location.href = ''
    } catch (error) {
        console.log(error)
    }
}

const handleIsSetModify = item => {
    state.showArea = SHOW_AREA_MAP.PASSWORD
    state.setPassword.resetTitle = '设置新密码'
    state.setPassword.isTips = item
}

// 登录按钮
async function submitLogin(accountFrom) {
    try {
        state.showArea = SHOW_AREA_MAP.LOGIN
        store.isCompletedLogin = false
        store.loginPlatform = ''
        updateLoginStatus('loading')
        // 账号加密
        const passWadData = {
            encoded: RSA.encrypt(JSON.stringify(accountFrom)),
        }

        const res = await http.post(`/api/${platform[store.sysIdent]}/auth/token`, passWadData)
        if (res.code !== 0) {
            updateLoginStatus('error')
            YMessage.error(res.message)
            return
        }
        updateLoginStatus('success')
        const { accessToken, refreshToken } = res.data
        store.setToken(accessToken, refreshToken)
        getCurrentUser()
    } catch (error) {
        console.log('error:', error)
        updateLoginStatus('error')
    }
}

// 选择学校中返回重新登录
function backLogin() {
    state.showArea = SHOW_AREA_MAP.LOGIN
    state.isScanCode = false
}

// 选择学校完按钮
async function selectSubmit(school) {
    try {
        updateLoginStatus('loading')
        const { id, isEnabled, children } = school
        const params = {
            schoolId: id,
            platform: 'user', // TODO: 系统标识待确认
        }
        const res = await http.get('/cloud/menu/checkUserLogin', params)
        if (res.code !== 0) {
            updateLoginStatus('error')
            YMessage.error(res.message)
            return
        }
        if (isEnabled !== undefined && !isEnabled) {
            updateLoginStatus('error')
            YMessage.error('该学校被禁用')
        } else if (children.length && !children[0].isEnabled) {
            updateLoginStatus('error')
            YMessage.error('该教职工账号已停用')
        } else {
            // 获取到路由信息
            // $ 重新刷新用户数据，schoolId为当前选中的id
            const userRes = await http.get('/cloud/user/getCurrentUser')
            store.userInfo = userRes.data
            // $ 记录当前登录平台
            store.loginPlatform = 'user' // TODO:系统标识待确认
            store.isCompletedLogin = true
            window.location.href = '/'
        }
        updateLoginStatus('success')
    } catch (error) {
        backLogin()
        updateLoginStatus('error')
    }
}
</script>

<style lang="less" scoped>
.login_box {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100vw;
    height: 100vh;
    background-size: 100% 100%;
    padding-left: 251px;

    .introduction {
        display: flex;
        align-items: center;
        img {
            width: 747px;
            height: 472px;
        }

        .title {
            font-size: 48px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.88);
            line-height: 67px;
        }

        .description {
            font-size: 32px;
            font-weight: 400;
            color: #1f1f1f;
            line-height: 45px;
            margin-top: 7px;
        }
    }

    .form_box {
        background: #ffffff;
        width: 380px;
        height: 416px;
        padding: 32px;
        box-shadow: 0px 7px 16px 0px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        position: absolute;
        top: 50%;
        left: 75%;
        transform: translate3d(-50%, -50%, 0);

        .form_title {
            text-align: left;
            font-size: 22px;
            font-weight: 800;
            margin: 22px 0px 0px 0px;
        }

        .scan_code {
            width: 56px;
            height: 56px;
            position: absolute;
            top: 0;
            right: 0;
            cursor: pointer;
            background-repeat: no-repeat;
            background-size: contain;
        }
    }

    .copyright {
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        font-weight: 400;
        color: rgba(102, 102, 102, 0.88);
    }
}
</style>
