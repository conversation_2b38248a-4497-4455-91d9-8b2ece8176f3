<template>
    <div>
        <a-modal v-model:open="show" :centered="true" title="" @cancel="submit" @ok="submit" :width="700">
            <div class="modal_box">
                <Protocol />
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import Protocol from './protocol.vue'
const emit = defineEmits(['handle'])
const src = ref(import.meta.env.VITE_BASE_API_AGREEMENT + '/html/protocol.html')
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
})

const show = computed(() => {
    return props.show
})

function submit() {
    emit('handle')
}
</script>

<style lang="less" scope>
.modal_box {
    height: 700px;
    overflow: auto;
}
</style>
