<template>
    <a-carousel
        class="select_school"
        arrows
        :dots="false"
        :slides-to-show="3"
        ref="selectSchoolRef"
        :after-change="afterChange"
        :dontAnimate="true"
    >
        <template #prevArrow>
            <div v-show="prevShow" class="custom-slick-arrow" style="left: -25px; z-index: 1">
                <left-circle-outlined class="circle-outlined" />
            </div>
        </template>
        <template #nextArrow>
            <div v-show="nextShow" class="custom-slick-arrow" style="right: -25px">
                <right-circle-outlined class="circle-outlined" />
            </div>
        </template>
        <div v-for="(item, index) in previewImgs" :key="index" @click="afterChange(index)" class="item_school">
            <img v-if="!item.show" class="img" :src="item.schoolLogo ? item.schoolLogo : defaultLogo" alt="" />
            <p class="school_name">{{ item.schoolName }}</p>
        </div>
    </a-carousel>
    <a-button size="large" class="btn_class" type="primary" :loading="loading" @click="submit">登录</a-button>
    <a-button class="button-link" type="link" p-0 @click="backtrack">返回</a-button>
</template>
<script setup>
import defaultLogo from '@/assets/images/admin/login_default_logo.png'
const emit = defineEmits(['submit', 'back'])
const selectSchoolRef = ref(null)
const prevShow = ref(false) // 左边切换
const nextShow = ref(true) // 右边切换
const previewImgs = ref([]) // 数组
const index = ref(1) // 记录切换的index
const props = defineProps({
    data: {
        type: Array,
        default: () => [],
    },
    loading: {
        type: Boolean,
        default: false,
    },
})

// 学校数组
const data = computed(() => {
    return props.data
})

// 登录按钮
const loading = computed(() => {
    return props.loading
})

// 切换学校
const afterChange = res => {
    if (data.value.length === 1) {
        return
    }
    selectSchoolRef.value.goTo(res - 1)
    index.value = res + 1
    if (!res) {
        prevShow.value = false
        nextShow.value = true
        return
    }
    if (res == previewImgs.value.length - 3) {
        prevShow.value = true
        nextShow.value = false
    }
}

function submit() {
    const school = previewImgs.value[index.value]
    emit('submit', school)
}

function backtrack() {
    emit('back')
}

onMounted(() => {
    const obj = { logo: '', name: '', show: true }
    previewImgs.value = [...data.value]
    previewImgs.value.push(obj)
    previewImgs.value.unshift(obj)
})
</script>

<style scoped lang="less">
.select_school {
    width: 315px;
    height: 250px;
    display: flex;
    align-items: center;
    margin: 0 auto;

    .school_name {
        max-height: 62px;
        font-size: 14px;
        padding: 8px 0 10px;
    }
}

:deep(.slick-slider) {
    width: 100%;
    height: 250px;
    display: flex;
    align-items: center;
}

:deep(.slick-list) {
    height: 100%;
    .slick-track {
        padding-top: 11px;
        margin-top: 58px;
    }

    .slick-slide {
        text-align: center;
        width: 89px !important;
        margin: 0 8px;

        .img {
            width: 68px;
            height: 68px;
            margin: 12px auto 0 auto;
            border-radius: 50%;
        }
    }

    .slick-active {
        opacity: 0.6;
    }

    .slick-current + .slick-active {
        transform: scale(1.25);
        opacity: 1;
    }
}

.ant-carousel :deep(.slick-arrow.custom-slick-arrow) {
    width: 28px;
    height: 28px;
    font-size: 28px;
    border-radius: 50%;
    color: '#d9d9d9';
    background: '#d9d9d9';
    opacity: 0.3;
    z-index: 1;

    &:before {
        display: none;
    }

    &:hover {
        opacity: 0.5;
    }
}

.circle-outlined {
    color: #fff;
    font-size: 28px;
    background: #d9d9d9;
    border-radius: 100%;
}

.item_school {
    cursor: pointer;
}
.btn_class {
    width: 100%;
    margin: 20px auto;
}
</style>
