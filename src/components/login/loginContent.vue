<template>
    <div class="login_con">
        <!-- 账号密码登录 -->
        <a-form ref="loginFormRef" :model="accountFrom" name="accountFrom" class="accountFrom">
            <a-form-item name="username" :rules="[{ required: true, trigger: 'blur', message: '请输入你的手机号或用户名' }]">
                <div>
                    <a-input size="large" v-model:value.trim="accountFrom.username" placeholder="请输入你的手机号或用户名" />
                </div>
            </a-form-item>
            <a-form-item name="password" :rules="[{ required: true, trigger: 'blur', message: '请输入密码' }]">
                <a-input-password
                    size="large"
                    v-model:value.trim="accountFrom.password"
                    placeholder="请输入密码"
                    @keydown="handleKeydown"
                    autocomplete="on"
                />
            </a-form-item>
            <a-form-item class="password_operate">
                <a-checkbox v-model:checked="store.remember">
                    <span class="passText">记住密码</span>
                </a-checkbox>
            </a-form-item>
            <a-form-item mb-24 mt-38>
                <a-button size="large" m-0 style="width: 100%" type="primary" @click="submit" :loading="loading">
                    {{ loginText }}
                </a-button>
            </a-form-item>
            <a-form-item class="password_operate">
                <a-checkbox v-model:checked="agreement" @change="changeAgreement"></a-checkbox>
                <div class="readme" :class="{ shake: state.shake }">
                    我已认真阅读，理解并同意
                    <span class="agreement" @click="agreementShow = true">《用户协议及隐私权政策》。</span>
                </div>
            </a-form-item>
            <AgreementModal :show="agreementShow" @handle="handleAgreement" />
        </a-form>
    </div>
</template>

<script setup name="accountFrom">
import { getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()

const store = useStore()

const emit = defineEmits(['submit'])
const props = defineProps({
    // 按钮文案
    loginText: {
        type: String,
        default: '登 录',
    },
    loading: {
        type: Boolean,
        default: false,
    },
})

const loginFormRef = ref(null)
const agreement = ref(store.agreement) // 是否已经同意隐私政策
const agreementShow = ref(false) // 协议弹框
const accountFrom = ref({
    username: '',
    password: '',
})

const state = reactive({
    shake: false,
    timer: null,
})

const loading = computed(() => {
    return props.loading
})
const loginText = computed(() => {
    return props.loginText
})

// 滑块验证
// 定义回调函数
function callback(res) {
    // ret 验证结果，0：验证成功。2：用户主动关闭验证码。
    if (res.ret === 0) {
        const str = `【randstr】->【${res.randstr}'】      【ticket】->【'${res.ticket}'】'`
        const ipt = document.createElement('input')
        ipt.value = str
        document.body.appendChild(ipt)
        ipt.select()
        document.execCommand('Copy')
        document.body.removeChild(ipt)
        accountFrom.value.randStr = res.randstr
        accountFrom.value.ticket = res.ticket
        emit('submit', accountFrom.value)
    }
}
// 定义验证码js加载错误处理函数
function loadErrorCallback() {
    // 生成容灾票据或自行做其它处理
    // eslint-disable-next-line node/no-callback-literal
    callback({
        ret: 0,
        randstr: atob(proxy.captchaId), // 本次验证的随机串，后续票据校验时需传递该参数。
        ticket: atob(proxy.ticket), // 验证成功的票据，当且仅当 ret = 0 时 ticket 有值。
        CaptchaType: 1,
        errorCode: 1001,
        errorMessage: 'jsload_error',
    })
}
// 登录验证
function onVerification() {
    try {
        // https://cloud.tencent.com/document/product/1110/36841
        // 生成一个验证码对象
        // eslint-disable-next-line no-undef
        const captcha = new TencentCaptcha(atob(proxy.captchaId), callback, {
            needFeedBack: false,
        })
        // 调用方法，显示验证码
        captcha.show()
    } catch (error) {
        // 加载异常，调用验证码js加载错误处理函数
        loadErrorCallback()
    }
}

onMounted(() => {
    if (store.remember) {
        // 获取缓存账号密码
        const { username, password } = store.accountFrom
        accountFrom.value.username = username
        accountFrom.value.password = password
    }
})

// 确认登录
function submit() {
    loginFormRef.value
        .validate()
        .then(() => {
            if (!agreement.value) {
                state.shake = true
                // 使用动画完成抖动效果
                setTimeout(() => {
                    state.shake = false // 还原状态
                }, 300)
                return
            }
            // !!!缓存账号密码数据 -修改密码的账号信息是从这里获取的
            store.accountFrom = accountFrom.value
            // *********-=4678

            // 登录验证
            // const { MODE } = import.meta.env
            // if (['production', 'development', 'uat', 'uatrelease'].includes(MODE)) {
            //     onVerification()
            // } else {
            //     emit('submit', accountFrom.value)
            // }
            emit('submit', accountFrom.value)
        })
        .catch(err => {
            console.log('err: ', err)
        })
}
// 密码框回车键
function handleKeydown(e) {
    const key = e.keyCode
    if (key === 13) {
        submit()
    }
}

function changeAgreement(e) {
    store.setAgreement = e.target.checked
}

function handleAgreement() {
    store.setAgreement = true
    agreement.value = true
    agreementShow.value = false
}
</script>

<style scoped lang="less">
.login_con {
    margin-top: 40px;

    .accountFrom {
        :deep(.ant-form-item) {
            &:nth-of-type(1) {
                height: 65px;
            }

            &:nth-of-type(2) {
                height: 65px;
            }
        }
    }
}

.password_operate {
    margin-bottom: 10px;

    :deep(.ant-form-item-control-input-content) {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
    }

    .passText {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
        line-height: 20px;
    }

    .readme {
        width: 364px;
        height: 48px;
        font-size: 14px;
        font-weight: 400;
        color: #999999;
        line-height: 24px;
        margin-left: 10px;
    }

    .agreement {
        width: 364px;
        height: 48px;
        font-size: 14px;
        font-weight: 400;
        color: var(--primary-color);
        line-height: 24px;
        cursor: pointer;
    }

    .shake {
        animation: shake 0.3s;
    }

    @keyframes shake {
        0% {
            transform: translateX(0);
        }

        20% {
            transform: translateX(-15px);
        }

        40% {
            transform: translateX(15px);
        }

        60% {
            transform: translateX(-15px);
        }

        80% {
            transform: translateX(15px);
        }

        100% {
            transform: translateX(0);
        }
    }
}

.login_code {
    position: relative;
    text-align: center;

    .expired,
    .code {
        width: 184px;
        height: 184px;
    }

    .refresh {
        font-size: 13px;
        margin: 30px 0;
    }

    .expired {
        background: #ffffff99;
        color: @error-color;
        line-height: 184px;
        position: absolute;
        top: 0;
        left: 98px;
        color: #000;
        font-size: 26px;
    }
}
</style>
