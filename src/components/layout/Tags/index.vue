<template>
    <div p-t4 p-b4>
        <ScrollX ref="scrollXRef">
            <a-space :size="0">
                <template v-for="(tag, index) in store.tags" :key="tag.key">
                    <a-dropdown :trigger="['contextmenu']">
                        <a-tag
                            ref="tagRefs"
                            :class="['yd_tag', { activeClass: tag.key === activeTag?.key }]"
                            :key="tag.key"
                            :closable="store.tags.length !== 1"
                            @click="e => handleTagClick(e, tag.key)"
                            @close.prevent="close(index)"
                            @contextmenu.prevent="handleContextMenu(tag)"
                        >
                            {{ tag.title }}
                        </a-tag>
                        <template #overlay>
                            <ContextMenu :ctag="ctag" />
                        </template>
                    </a-dropdown>
                </template>
            </a-space>
        </ScrollX>
    </div>
</template>

<script setup>
import ScrollX from './ScrollX.vue'
const route = useRoute()
const router = useRouter()
const scrollXRef = ref(null)
const tagRefs = ref([])
const store = useStore()
const ctag = ref({})
const activeTag = computed(() => {
    return store.tags.find(i => i.key == route.name)
})
const close = async index => {
    if (store.tags[index].key == route.name) {
        let item = store.tags[index + 1] || store.tags[index - 1]
        router.push({ name: item.key })
        store.tags.splice(index, 1)
    } else {
        store.tags.splice(index, 1)
    }
}

watch(
    () => route.path,
    () => {
        if (route.name == 'redirect' || route.name == '404' || route.name == 'no-auth') {
            return false
        }
        let index = store.tags.findIndex(i => i.key == route.name)
        const item = { title: route.meta.title, key: route.name }
        if (index == -1) {
            if (store.tags.length > 10) {
                store.tags.splice(1, 1)
                store.tags.push(item)
            } else {
                store.tags.push(item)
            }
        } else {
            store.tags.splice(index, 1, item)
        }
    },
    { immediate: true },
)
const handleTagClick = (e, path) => {
    router.push({ name: path })
}
// 右击菜单
async function handleContextMenu(tagItem) {
    ctag.value = tagItem
}
</script>

<style lang="less" scoped>
.yd_tag {
    height: 26px;
    padding: 0px 8px 0px 8px;
    background-color: #fff;
    line-height: 24px;
    border-radius: 4px;
    cursor: pointer;
    :deep(.ant-tag-close-icon) {
        padding-left: 4px;
    }
}
.activeClass {
    color: #fff;
    background-color: var(--primary-color);
    :deep(.ant-tag-close-icon) {
        svg {
            color: #fff;
        }
    }
}
</style>
