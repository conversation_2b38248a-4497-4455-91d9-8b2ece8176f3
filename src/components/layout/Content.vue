<template>
    <a-layout-content :class="['layout_container']">
        <a-spin :spinning="mainStore.spinning">
            <router-view v-slot="{ Component, route }">
                <transition enter-active-class="animate__animated animate__fadeIn">
                    <div :class="['scrollbar', { router_view: route.name !== 'home' }]">
                        <KeepAlive :include="include" :max="10">
                            <component :is="Component" :key="route.fullPath" />
                        </KeepAlive>
                    </div>
                </transition>
            </router-view>
        </a-spin>
    </a-layout-content>
</template>
<script setup>
const mainStore = useStore()
const include = []
const height = 'calc(100vh - 98px)'
</script>
<style lang="less" scoped>
.layout_container {
    margin: 0 10px 10px 10px;
    .router_view {
        background-color: #fff;
        height: v-bind('height');
        border-radius: 4px;
    }
}
</style>
