<template>
    <a-modal v-model:open="state.open" title="修改密码" @ok="handleOk" @cancel="cancel">
        <a-form p-20 ref="formRef" layout="vertical" name="custom-validation" :rules="rules" :model="state.formState">
            <a-form-item mb-10 label="旧密码:" name="oldPwd">
                <a-input-password
                    v-model:value.trim="state.formState.oldPwd"
                    type="password"
                    placeholder="请输入旧密码"
                    autocomplete="off"
                />
            </a-form-item>
            <a-form-item mb-10 label="新密码:" name="newPwd">
                <a-input-password
                    v-model:value.trim="state.formState.newPwd"
                    type="password"
                    placeholder="请输入新密码"
                    autocomplete="off"
                />
            </a-form-item>
            <a-form-item mb-10 label="确认密码:" name="confirmPwd">
                <a-input-password
                    v-model:value.trim="state.formState.confirmPwd"
                    type="password"
                    placeholder="请再次输入新密码"
                    autocomplete="off"
                />
            </a-form-item>
            <div>
                <ExclamationCircleFilled />
                密码需要为字母+数字,长度6-20位
            </div>
        </a-form>
    </a-modal>
</template>
<script setup>
const emit = defineEmits(['cancel'])
const router = useRouter()

const state = reactive({
    open: false,
    formState: {},
})

const rg = /^\S*(?=\S{6,20})(?=\S*\d)(?=\S*[a-zA-Z])\S*$/
const validatePass = async (_rule, value) => {
    if (value === '') {
        return Promise.reject('请输入新密码')
    } else {
        if (state.formState.newPwd !== '' && !rg.test(state.formState.newPwd)) {
            return Promise.reject('密码需要为字母+数字,长度6-20位')
        } else {
            return Promise.resolve()
        }
    }
}

const validatePass2 = async (_rule, value) => {
    if (value === '') {
        return Promise.reject('请再次输入新密码')
    } else if (value !== state.formState.newPwd) {
        return Promise.reject('两次输入密码不一致')
    } else {
        return Promise.resolve()
    }
}

const rules = {
    oldPwd: [{ required: true, message: '请输入旧密码', trigger: ['change', 'blur'] }],
    newPwd: [{ required: true, validator: validatePass, trigger: ['change', 'blur'] }],
    confirmPwd: [{ required: true, validator: validatePass2, trigger: ['change', 'blur'] }],
}

const formRef = ref(null)
const handleOk = () => {
    formRef.value.validate().then(() => {
        http.post('/marketing/adminUser/updatePassword', state.formState).then(() => {
            YMessage.success('密码修改成功,请重新登录')
            formRef.value.resetFields()
            setTimeout(() => {
                router.replace('/login')
            }, 1000)
        })
    })
}

const cancel = () => {
    state.open = false
    state.formState = {}
    formRef.value.resetFields()
    emit('cancel')
}

const showModel = () => {
    state.open = true
}

defineExpose({
    showModel,
})
</script>
<style scoped lang="less"></style>
