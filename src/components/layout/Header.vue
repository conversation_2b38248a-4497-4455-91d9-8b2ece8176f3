<template>
    <a-layout-header class="layout_header">
        <div class="header_left">
            <slot name="headerLeft"></slot>
        </div>
        <div class="header_right">
            <a-dropdown arrow>
                <span class="header_right_item">
                    <a-avatar :size="26" :src="mainStore.userInfo?.avatar || avatar"></a-avatar>
                    <span pl-4>{{ mainStore.userInfo?.name }}</span>
                    <CaretDownOutlined style="margin-left: 5px" />
                </span>
                <template #overlay>
                    <a-menu>
                        <a-menu-item @click="handleLoginOut">
                            <template #icon>
                                <i class="iconfont icon-tuichu"></i>
                            </template>
                            退出登录
                        </a-menu-item>
                    </a-menu>
                </template>
            </a-dropdown>
        </div>
    </a-layout-header>
</template>

<script setup>
import avatar from '@/assets/images/avatar.png'
const mainStore = useStore()
const state = reactive({
    open: false,
    checkGroup: [],
    schoolId: '',
    loading: false,
})

// 获取用户信息
const getCurrentUserFn = () => {
    http.get('/cloud/user/getCurrentUser', {}).then(({ data }) => {
        mainStore.userInfo = data
    })
}
const handleLoginOut = () => {
    mainStore.clearUser()
    window.close()
}

onMounted(() => {
    // 测试
    let params = getUrlParams()
    params.token && getCurrentUserFn()
    // state.schoolId = '1694279322606526466'
    state.schoolId = params.schoolId || mainStore.userInfo.schoolId
})
</script>

<style lang="less" scoped>
.layout_header {
    background: #fff;
    padding: 0;
    height: 48px;
    line-height: 48px;
    display: flex;
    justify-content: space-between;

    .header_left {
        display: flex;
        align-items: center;

        .btn_item {
            display: inline-block;
            margin-left: 48px;
            cursor: pointer;

            &:hover {
                color: var(--primary-color);
            }

            &:first-of-type {
                margin-left: 0px;
            }
        }
    }

    .header_right {
        margin-right: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .message_btn {
            position: relative;
            display: flex;
            align-items: center;
            margin-right: 20px;
            padding: 0;

            .message_dot {
                display: flex;
                align-items: center;
                justify-content: center;
                position: absolute;
                top: 0;
                transform: translateX(40%);
                min-width: 16px;
                height: 16px;
                background: #ff4d4f;
                border-radius: 16px;
                border: 1px solid #ffffff;
                font-size: 10px;
                color: #fff;
                padding: 0 5px;
            }
        }

        .header_right_l {
            display: flex;
            align-items: center;

            & > span {
                color: #2c2c2c;
                padding-right: 16px;
                margin-right: 16px;
                font-size: 14px;
                cursor: pointer;
                line-height: 18px;
                border-right: 1px solid #999999;

                &:hover {
                    color: var(--primary-color);
                }
            }
        }

        .header_right_item {
            margin: 0 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .header_right_item:hover {
            color: var(--primary-color);
        }

        .iconfont {
            font-size: 18px;
        }

        .dormitory-box {
            display: flex;
            align-items: center;
            height: 30px;
            box-sizing: border-box;
            background: #ebfaf5;
            border: 1px solid var(--primary-color);
            border-radius: 30px;
            padding: 0 14px;

            span {
                font-size: 14px;
                font-weight: 400;
                color: var(--primary-color);
            }
        }
    }

    .cloud_platform_btn {
        border-radius: 32px;
        color: var(--primary-color);
    }
}
</style>

<style lang="less">
.crm_message_content {
    width: 300px;
    background: #ffffff;
    box-shadow:
        0px 9px 28px 8px rgba(0, 0, 0, 0.05),
        0px 6px 16px 0px rgba(0, 0, 0, 0.08),
        0px 3px 6px -4px rgba(0, 0, 0, 0.12);
    padding: 18px 16px;
    border-radius: 10px;

    .message_headers {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .icon {
            font-size: 16px;
            color: #595959;
            cursor: pointer;
        }

        p {
            font-weight: 600;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.88);
        }
    }

    .message_list {
        border-top: 1px solid #e6e6e6;
        border-bottom: 1px solid #e6e6e6;
        margin: 12px 0;

        li {
            padding: 8px;
            margin: 8px 0;
            cursor: pointer;
            border-radius: 4px;

            &:hover {
                background: #eaf9f5;
            }
        }

        .title {
            font-weight: 400;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.88);
        }

        .time {
            font-weight: 400;
            font-size: 12px;
            color: #595959;
            margin-top: 4px;
        }

        .have_read {
            .title,
            .time {
                color: rgba(153, 153, 153, 0.88);
            }
        }
    }

    .message_footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-weight: 400;
        font-size: 12px;
        color: #595959;
        cursor: pointer;
        margin-left: auto;

        .icon {
            font-size: 20px;
            margin: 1px 0 0 2px;
        }
    }
}
</style>
