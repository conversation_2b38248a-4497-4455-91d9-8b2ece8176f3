<template>
    <a-layout-sider
        :collapsed="collapsed"
        theme="light"
        width="208"
        :trigger="null"
        collapsible
        class="scrollbar sider_container"
    >
        <a-menu
            v-model:selectedKeys="selectedKeys"
            v-model:openKeys="openKeys"
            mode="inline"
            @click="handleClick"
            class="yd_root_menu"
        >
            <template v-for="item in menuList" :key="item.name">
                <template v-if="!item.children?.length">
                    <a-menu-item :key="item.name" v-if="!item.meta.hidden">
                        <template #icon v-if="item.meta.icon">
                            <i class="sider" :class="['iconfont', item.meta.icon]"></i>
                        </template>
                        {{ item.meta.title }}
                    </a-menu-item>
                </template>
                <template v-else>
                    <SubMenu :menu-list="item" />
                </template>
            </template>
        </a-menu>
    </a-layout-sider>
</template>

<script setup>
import SubMenu from './SubMenu.vue'
import routeList from '@/router/getRoute'
import { useRoute, useRouter } from 'vue-router'

// *********************
// Hooks Function
// *********************
defineProps({
    collapsed: {
        type: Boolean,
        default: false,
    },
})
const router = useRouter()
const route = useRoute()
const menuList = ref([])
const openKeys = ref([])
const selectedKeys = ref(route.matched.map(i => i.name))
const mainStore = useStore()

// *********************
// Default Function
// *********************

const init = async () => {
    let [arr, _] = await routeList()
    arr = arr[0].children
    console.log(arr)
    menuList.value = arr
    // menuList.value = [
    //     {
    //         path: '/manage/home',
    //         name: 'home',
    //         meta: {
    //             title: '首页',
    //             icon: 'icon-xuexiaoguanli',
    //             isTreeMenu: true,
    //         },
    //     },
    //     {
    //         path: '/manage/equipmentManage',
    //         name: 'equipmentManage',
    //         meta: {
    //             title: '设备管理',
    //             icon: 'icon-xuexiaoguanli',
    //             isTreeMenu: true,
    //         },
    //     },
    //     {
    //         path: '/manage/keyword',
    //         name: 'keyword',
    //         meta: {
    //             title: '关键词管理',
    //             icon: 'icon-xuexiaoguanli',
    //             isTreeMenu: true,
    //         },
    //     },
    //     {
    //         path: '/manage/alarmRule',
    //         name: 'alarmRule',
    //         meta: {
    //             title: '告警规则',
    //             icon: 'icon-xuexiaoguanli',
    //             isTreeMenu: true,
    //         },
    //     },
    //     {
    //         path: '/manage/alarmReceive',
    //         name: 'alarmReceive',
    //         meta: {
    //             title: '告警接收',
    //             icon: 'icon-xuexiaoguanli',
    //             isTreeMenu: true,
    //         },
    //     },
    //     {
    //         path: '/manage/alarmList',
    //         name: 'alarmList',
    //         meta: {
    //             title: '告警列表',
    //             icon: 'icon-xuexiaoguanli',
    //             isTreeMenu: true,
    //         },
    //     },
    // ]
}
// *********************
// Life Event Function
// *********************

init()

// *********************
// Service Function
// *********************

watch(
    () => route.path,
    () => {
        selectedKeys.value = route.matched.map(i => i.name)
        openKeys.value = route.matched.map(i => i.name)
    },
    { immediate: true },
)

const handleClick = e => {
    router.push({ name: e.key })
}
</script>

<style lang="less" scoped>
.scrollbar {
    &::-webkit-scrollbar {
        width: 0px;
    }
}
.sider_container {
    box-shadow: 1px 0px 4px 0px #eceef1;
    position: relative;
    :deep(.ant-menu-inline),
    :deep(.ant-menu-vertical) {
        border-inline-end: none;
    }
    .logo {
        position: absolute;
        top: 0;
        z-index: 9;
        background-color: #fff;
        width: 208px;
        height: 49px;
        display: flex;
        align-items: center;
        padding-left: 16px;
        color: #2c2c2c;
        font-weight: 600;
    }
    .yd_root_menu {
        :deep(.ant-menu-item-selected) {
            &:before {
                content: '';
                width: 4px;
                height: 26px;
                background-color: var(--primary-color);
                position: absolute;
                top: 5px;
                left: 0;
                border-radius: 0px 2px 2px 0px;
            }
        }
    }
}
</style>
