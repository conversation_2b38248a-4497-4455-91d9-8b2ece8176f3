<template>
    <a-layout class="layou_box">
        <Header>
            <template #headerLeft>
                <div class="logo">
                    <img :src="logo" class="img" />
                    {{ '智慧点餐系统' }}
                </div>
            </template>
        </Header>
        <a-layout>
            <div class="sider_box">
                <sider class="reset-sider" :collapsed="mainStore.collapsed"></sider>
                <div class="collapsed_box" @click="() => (mainStore.collapsed = !mainStore.collapsed)">
                    <RightOutlined v-if="mainStore.collapsed" style="font-size: 12px" />
                    <LeftOutlined v-else style="font-size: 12px" />
                </div>
            </div>
            <a-layout class="scrollbar right_container">
                <Tags></Tags>
                <Content></Content>
            </a-layout>
        </a-layout>
    </a-layout>
</template>
<script setup>
const mainStore = useStore()
import logo from '@/assets/images/logo.png'
</script>

<style lang="less" scoped>
.layou_box {
    height: 100vh;

    .logo {
        display: flex;
        align-items: center;

        .img {
            width: 34px;
            height: 34px;
            margin-right: 10px;
            margin-left: 24px;
        }

        font-size: 16px;
        font-weight: 600;
    }

    svg {
        margin-left: 24px;
        margin-right: 16px;
        cursor: pointer;
    }

    .right_container {
        background-color: #f7f8fa;
        // min-width: 1360px;
    }

    .sider_box {
        position: relative;
        font-weight: 600;
        .collapsed_box {
            position: absolute;
            right: -12px;
            top: 52px;
            width: 12px;
            height: 32px;
            border-radius: 0 4px 4px 0;
            background: @primary-color;
            opacity: 0.3;
            color: @body-background;
            display: flex;
            align-items: center;
            z-index: 99;
            cursor: pointer;
        }
    }

    .reset-sider {
        height: 100%;
    }
}
</style>
