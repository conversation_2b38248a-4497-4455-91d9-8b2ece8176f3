<template>
    <a-sub-menu :key="menuList.name">
        <template #icon v-if="menuList.meta.icon">
            <i class="sider" :class="['iconfont', menuList.meta.icon]"></i>
        </template>
        <template #title>
            <span>{{ menuList.meta.title }}</span>
        </template>
        <template v-for="item in menuList.children" :key="item.path">
            <template v-if="!item.children?.length">
                <a-menu-item :key="item.name" v-if="!item.meta.icon">
                    <template #icon v-if="item.meta.icon">
                        <i class="sider" :class="['iconfont', item.meta.icon]"></i>
                    </template>
                    {{ item.meta.title }}
                </a-menu-item>
            </template>
            <template v-else>
                <sub-menu :menu-list="item" :key="item.name" />
            </template>
        </template>
    </a-sub-menu>
</template>
<script setup name="sub-menu">
// *********************
// Hooks Function
// *********************

defineProps({
    menuList: {
        type: Object,
        default: () => ({}),
    },
})

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************
</script>
<style lang="less" scoped></style>
