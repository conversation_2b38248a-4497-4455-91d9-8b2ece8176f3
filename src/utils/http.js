import { message } from 'ant-design-vue'
import { debounce } from '@/utils/common.js'
import axios from 'axios'
axios.defaults.baseURL = import.meta.env.VITE_BASE_API
axios.defaults.timeout = 50000

/** 参数是否一致 */
let postStr = {}
const isRepeat = (url, data) => {
    let flag = true
    const key = url + JSON.stringify(data)
    if (postStr[key]) {
        flag = false
    } else {
        flag = true
        postStr[key] = true
    }
    return flag
}
const getToken = () => {
    const store = useStore()
    if (store.token) {
        return store.token.includes('Bearer') ? store.token : `Bearer ${store.token}`
    } else {
        window.location.replace(`/#/login/${store.sysIdent}`)
        // window.location.replace(`/#/login`)
    }
}

// 请求拦截器
axios.interceptors.request.use(
    config => {
        config.headers.Authorization = getToken()
        return config
    },
    error => {
        return Promise.reject(error)
    },
)

const tipMessage = debounce(data => {
    if (data?.message && data.message.length > 20) {
        message.error('系统异常，请联系管理员')
    } else {
        message.error(data?.message || '系统异常，请联系管理员')
    }
    console.log('error', data?.message || '系统异常，请联系管理员')
}, 1000)

// 分三个端 user:用户端  agent:代理商端  management:管理端
const platform = {
    user: 'school',
    agent: 'partner',
    management: 'admin',
}

const toLoginCode = [
    401, 1001001001, 1001001002, 1001001003, 1001001005, 1001001006, 1001001007, 1002002002, 1002002009, 1002002012, 1002002013,
    403,
]
// 响应拦截器
axios.interceptors.response.use(
    async response => {
        const store = useStore()
        const { data, config, status } = response
        if (config?.method == 'post') {
            // 计算当前URL传递的参数
            const key = config.url + config.data
            if (postStr[key]) {
                postStr[key] = null
            }
        }

        if (config?.responseType === 'arraybuffer') {
            return data
        }

        if (config?.responseType === 'blob') {
            return response
        }

        if (status == 200 && data.code == 0) {
            return data
        } else if (status == 200 && data.code == 1002002014) {
            if (window.location.href.indexOf(`/#/login/${store.sysIdent}`)) {
                // 本身在登录页(不跳转到没有权限页面)
                return data
            } else {
                window.location.replace('/#/no-auth')
            }
        } else if (toLoginCode.includes(data.code)) {
            store.clearUser()
            tipMessage(data)
            // d待处理
            window.location.replace(`/#/login/${store.sysIdent}`)
            return data
        } else {
            tipMessage(data)
            return Promise.reject(data)
        }
    },
    error => {
        const { config } = error
        if (config.method == 'post') {
            // 计算当前URL传递的参数
            const key = config.url + config.data
            if (postStr[key]) {
                postStr[key] = null
            }
        }
        return Promise.reject(error)
    },
)
const get = (url, params = {}, headers) => {
    return axios({
        method: 'get',
        url: url,
        params,
        headers,
    })
}
const post = (url, data, params, headers = { 'Content-Type': 'application/json' }) => {
    const flag = isRepeat(url, data)
    if (flag) {
        return axios({
            method: 'post',
            url: url,
            headers,
            data,
            params,
        })
    } else {
        return Promise.reject()
    }
}

const download = (
    url,
    method = 'post',
    data,
    name,
    fn,
    type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
) => {
    return axios({
        url,
        method,
        data,
        responseType: 'arraybuffer',
    }).then(blob => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type,
            }),
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', `${name}`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        fn && fn()
        setTimeout(() => window.URL.revokeObjectURL(url), 1000)
    })
}
const form = (url, data) => {
    let fd = new FormData()
    for (let key in data) {
        fd.append(key, data[key])
    }
    return axios({
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        url: url,
        data: fd,
    })
}

const put = (url, data) => {
    return axios({
        method: 'put',
        url: url,
        data,
    })
}

export default { get, post, download, form, put }
export { axios }
