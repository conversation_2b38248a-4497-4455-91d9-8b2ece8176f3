const systemDict = {}
export default async (type, refresh = false) => {
    if (systemDict[type] && !refresh) {
        return systemDict[type]
    } else {
        systemDict[type] = systemDict[type] || ref([])
        const { data } = await http.post('/marketing/systemDict/get', [type])
        systemDict[type].value = data && data.length > 0 ? data[0].list : []
        return systemDict[type]
    }
}
