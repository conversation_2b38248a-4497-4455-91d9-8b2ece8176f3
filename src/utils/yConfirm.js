import { Modal } from 'ant-design-vue'
import { createVNode } from 'vue'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'

export default (title, content, okText = '确 认', cancelText = '取 消') => {
    return new Promise((resolve, reject) => {
        Modal.confirm({
            title,
            icon: createVNode(ExclamationCircleFilled),
            content,
            okText,
            cancelText,
            onOk() {
                resolve(true)
            },
            onCancel() {
                resolve(false)
            },
        })
    })
}
