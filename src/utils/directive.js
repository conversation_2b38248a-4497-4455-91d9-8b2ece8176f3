let store
export default app => {
    app.directive('focus', {
        mounted: el => el.focus(),
    })
    app.directive('auth', {
        // !!!注意: v-auth 和 v-if不能绑定到同一个元素上. 否则vue报错
        mounted(el, binding) {
            if (!binding.value) {
                return
            }
            if (store) {
                if (!store.Perms.includes(binding.value)) {
                    el.parentNode.removeChild(el)
                }
            } else {
                store = useStore()
                if (!store.Perms.includes(binding.value)) {
                    el.parentNode.removeChild(el)
                }
            }
        },
    })
    app.directive('scrollTo', {
        updated(el, binding) {
            if (binding.value) {
                const { x, y } = binding.value
                el.scrollTo(x, y)
            } else {
                el.scrollTo(0, 0)
            }
        },
    })
}
