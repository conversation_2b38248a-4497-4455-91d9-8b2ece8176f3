/**
 * @desc  函数防抖
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(method, wait, immediate) {
    let timeout
    return function (...args) {
        let context = this
        if (timeout) {
            clearTimeout(timeout)
        }
        // 立即执行需要两个条件，一是immediate为true，二是timeout未被赋值或被置为null
        if (immediate) {
            /**
             * 如果定时器不存在，则立即执行，并设置一个定时器，wait毫秒后将定时器置为null
             * 这样确保立即执行后wait毫秒内不会被再次触发
             */
            let callNow = !timeout
            timeout = setTimeout(() => {
                timeout = null
            }, wait)
            if (callNow) {
                method.apply(context, args)
            }
        } else {
            // 如果immediate为false，则函数wait毫秒后执行
            timeout = setTimeout(() => {
                /**
                 * args是一个类数组对象，所以使用fn.apply
                 * 也可写作method.call(context, ...args)
                 */
                method.apply(context, args)
            }, wait)
        }
    }
}

/**
 *
 * @param {HTMLElement} el
 * @param {Function} cb
 * @return {ResizeObserver}
 */
export function useResize(el, cb) {
    const observer = new ResizeObserver(entries => {
        cb(entries[0].contentRect)
    })
    observer.observe(el)
    return observer
}
//  深拷贝
export function deepClone(obj) {
    return JSON.parse(JSON.stringify(obj))
}

// 获取url参数
export function getUrlParams() {
    const url = window.location.href
    let urlStr = url.split('?')[1]
    if (!urlStr) return {}
    let obj = {}
    let paramsArr = urlStr.split('&')
    for (let i = 0, len = paramsArr.length; i < len; i++) {
        let arr = paramsArr[i].split('=')
        obj[arr[0]] = arr[1]
    }
    return obj
}
// 生成随机id
export function generateRandomID() {
    const date = +new Date()
    const num = date * (Math.random() * 1000).toFixed(0)
    return num.toString(36).substring(2)
}

/** 下载文件 */
export async function downloadFile(url, filename = '') {
    try {
        const response = await axios.get(url, {
            responseType: 'blob',
        })
        const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(link.href)
        YMessage.success('下载成功')
    } catch (error) {
        console.log('error: ', error)
        YMessage.error('下载失败')
    }
}

/** 获取静态文件地址 */
export function getStaticFile(fileName) {
    return `${import.meta.env.VITE_BASE_STATIC}/${fileName}`
}

/**手机号加* */
export function hideMiddleFourDigits(phone) {
    // 确保手机号是字符串格式
    const phoneStr = String(phone)
    // 使用正则表达式替换中间四位
    return phoneStr.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/** 复制链接 */
export function copyLink(text) {
    const textarea = document.createElement('textarea') // 构建textarea
    textarea.value = text // 设置内容
    document.body.appendChild(textarea) // 添加临时实例
    textarea.select() // 选择实例内容
    document.execCommand('Copy') // 执行复制
    document.body.removeChild(textarea) // 删除临时实例
}

// 将秒转换为分钟和秒
export function convertSeconds(seconds) {
    if (!seconds) return ' '
    var minutes = Math.floor(seconds / 60) // 计算分钟数
    var remainingSeconds = seconds % 60 // 计算剩余的秒数
    if (minutes) {
        return minutes + '分钟 ' + remainingSeconds + '秒'
    } else {
        return remainingSeconds + '秒'
    }
}

// 保留两位小数
export function roundFixedFn(num, num2) {
    let temp = Math.round(num * 100) // 先乘以100，再四舍五入取整
    let result = temp / 100 // 再除以100还原
    return result.toFixed(num2) // 输出 "3.14"，也可以直接使用 result.toString()获取字符串形式
}
